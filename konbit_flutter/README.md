# konbit_flutter

Download flutter dependencies for this project with this command below within the flutter project directory
where you see pubspec.yml file:
```shell
flutter pub get
```

This is a flutter project. In order to build it you need to run this command first within
the project root folder to generate data models and get rid of android studio or vscode errors:
```shell
dart run build_runner build -d
```

Then you can start running the project :
```shell
flutter run
```

or Compiling and building the Android APK
Then you can start running the project :
```shell
flutter build apk
```

If you're experiencing gradle build issues due to Java SDK compatibility, make sure your java version
matches the one required by gradle. From this project, go into ***android*** directory and run this command below
```shell
cd android
./gradlew wrapper --gradle-version=8.0
```

In case the build is still failing, you can remove this directory:
```shell
rm -rfi android/.gradle
```

And then relaunch the build process

See details and way to fix it through this link :
[https://docs.flutter.dev/release/breaking-changes/android-java-gradle-migration-guide](https://docs.flutter.dev/release/breaking-changes/android-java-gradle-migration-guide)

