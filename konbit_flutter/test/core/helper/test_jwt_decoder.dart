import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:test/test.dart';

void main() {
  const String token =
      'eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJRaEJualV2TVRJcThnMS1YTi1mZWZjeDRVZEdEcXEtOVY3RnJmTDZyV1RBIn0'
      '.****************************************************************************************************************'
      'IjoiaHR0cDovL2xvY2FsaG9zdDo4MDgxL3JlYWxtcy9Lb25iaXQiLCJhdWQiOiJhY2NvdW50Iiwic3ViIjoiZjpkNDc1NzA2YS1mMzllLTRjMDMtYj'
      'lmYi1iNDMzMThmMmU2MzY6a2VsaSIsInR5cCI6IkJlYXJlciIsImF6cCI6ImtvbmJpdC1jbGllbnQiLCJzaWQiOiIxMjkzNmFkZi02ZjMzLTQ2ODMtY'
      'TI0Yi0wZmRjMTdkODYwYjAiLCJhY3IiOiIxIiwiYWxsb3dlZC1vcmlnaW5zIjpbIi8qIl0sInJlYWxtX2FjY2VzcyI6eyJyb2xlcyI6WyJvZmZsaW5lX'
      '2FjY2VzcyIsInVtYV9hdXRob3JpemF0aW9uIl19LCJyZXNvdXJjZV9hY2Nlc3MiOnsiYWNjb3VudCI6eyJyb2xlcyI6WyJtYW5hZ2UtYWNjb3VudCIsIm'
      '1hbmFnZS1hY2NvdW50LWxpbmtzIiwidmlldy1wcm9maWxlIl19fSwic2NvcGUiOiJlbWFpbCBwcm9maWxlIiwicm9sZSI6InJlZ3VsYXIiLCJlbWFpbF92'
      'ZXJpZmllZCI6dHJ1ZSwicHJlZmVycmVkX3VzZXJuYW1lIjoia2VsaSIsImVuYWJsZWQiOnRydWV9.aqJx6mDB3ctsb0OArHooYHIqrLNWN4iFwC79sUbYLz'
      'pqv85P8p42IE7O-pIFYZIYZs4MR-ZNTepNjCfMX9jEiT3xHbwAd0Bkf_Ec0FQWpa1Vcko6Fh9ICo2IAxVvUdZXj78QfNzXILMFX2MGYBq1sZP84LbB3XTK2m'
      '1CiqSfB3NJI5QsA5OUqeR1e5QDie_CILBk6C2jI6wVm2Z5jAyaMjsqO1QTkqusTaClqaBxtJpFnDzdDTb-FMs0_Yhd5OQ7MrqfdNWSxJwSslCL7CsXI0ERnCE4'
      'yCKPF0ufrtFV0pajFCDyCTqJWRNsUrN-OKsXZAI6r4XS3H677GmHyg2h1Q';

  test("Test Token Decoder", () {
    Map<String, dynamic> decodedToken = JwtDecoder.decode(token);
    logger.info(decodedToken.toString());
    expect(decodedToken.containsKey('preferred_username'), true);
    expect(decodedToken['preferred_username'], 'keli');
  });
}
