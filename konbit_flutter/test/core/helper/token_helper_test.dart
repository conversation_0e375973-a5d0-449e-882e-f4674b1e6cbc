import 'dart:io';

import 'package:dcli/dcli.dart' as dcli;
import 'package:dio/dio.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:konbit_flutter/core/api/dio_builder.dart';
import 'package:konbit_flutter/core/api/user_api.dart';
import 'package:konbit_flutter/core/database/db_helper.dart';
import 'package:konbit_flutter/core/database/jwt_token_dao.dart';
import 'package:konbit_flutter/core/helper/token_helper.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/core/model/db_entity/jwt_token_entity.dart';
import 'package:konbit_flutter/core/model/db_entity/jwt_token_entity_builder.dart';
import 'package:konbit_flutter/core/model/output/jwt_token.dart';
import 'package:mockito/annotations.dart';
import 'package:mockito/mockito.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:test/test.dart';

import 'token_helper_test.mocks.dart';

@GenerateNiceMocks([MockSpec<JwtTokenDao>()])
void main() async {
  late Dio dio;
  late DioAdapter dioAdapter;
  late TokenHelper tokenHelper;

  String dbFile = dcli.createTempFile(suffix: '.db');
  logger.info('Database file : $dbFile');
  sqfliteFfiInit();
  var databaseFactory = databaseFactoryFfi;

  final Database db = await databaseFactory.openDatabase(dbFile);
  await db.execute(tblSqlScript);

  final JwtTokenDao tokenDao = JwtTokenDao(database: db);

  group("Token Helper Test", () {
    setUp(
      () {
        dio = DioBuilder.create(retryEnabled: false).build();

        dioAdapter = DioAdapter(
            dio: dio,
            matcher: const UrlRequestMatcher(matchMethod: true),
            printLogs: true);
        tokenHelper = TokenHelper(tokenDao: tokenDao, dio: dio);
      },
    );

    test("No Token test", () async {
      final token = await tokenHelper.getOrDownloadToken();
      expect(token, null);
    });

    test("DB Token test", () async {
      final jwtToken = JwtToken(
          accessToken: "accessToken",
          refreshToken: "refreshToken",
          tokenType: "JWT",
          expiresIn: 60,
          notBeforePolicy: 0,
          refreshExpiresIn: 180,
          scope: "scope",
          sessionState: "sessionState");
      final tokenEntity = JwtTokenEntityBuilder.create(jwtToken).build();
      tokenDao.saveToken(tokenEntity);

      final newToken = await tokenHelper.getOrDownloadToken();
      logger.info("New token : ${newToken}");
      expect(newToken != null, true);
    });

    test("Download Token Test", () async {
      final jwtToken = JwtToken(
          accessToken: "accessToken",
          refreshToken: "refreshToken",
          tokenType: "JWT",
          expiresIn: 1,
          notBeforePolicy: 0,
          refreshExpiresIn: 60,
          scope: "scope",
          sessionState: "sessionState");

      final tokenEntity = JwtTokenEntityBuilder.create(jwtToken).build();
      tokenDao.saveToken(tokenEntity);

      final newJwtToken = JwtToken(
          accessToken: "accessTokenUpdated",
          refreshToken: "refreshTokenUpdated",
          tokenType: "JWT",
          expiresIn: 120,
          notBeforePolicy: 0,
          refreshExpiresIn: 240,
          scope: "scope",
          sessionState: "sessionState");

      dioAdapter.onPost('${UserApi.baseUri}/refresh_token', (server) {
        server.reply(200, newJwtToken);
      });

      //Let's wait for the previous jwt token to expire before calling refresh token helper
      sleep(const Duration(seconds: 3));

      final newToken = await tokenHelper.getOrDownloadToken();
      expect(newToken, "accessTokenUpdated");
    });

    test("Download Token Test Force Refresh", () async {
      final jwtToken = JwtToken(
          accessToken: "accessToken",
          refreshToken: "refreshToken",
          tokenType: "JWT",
          expiresIn: 100,
          notBeforePolicy: 0,
          refreshExpiresIn: 60,
          scope: "scope",
          sessionState: "sessionState");

      final tokenEntity = JwtTokenEntityBuilder.create(jwtToken).build();

      final tokenDao = MockJwtTokenDao();
      tokenHelper = TokenHelper(tokenDao: tokenDao, dio: dio);
      when(tokenDao.getToken()).thenAnswer((_) async => tokenEntity);

      final newJwtToken = JwtToken(
          accessToken: "accessTokenUpdated",
          refreshToken: "refreshTokenUpdated",
          tokenType: "JWT",
          expiresIn: 120,
          notBeforePolicy: 0,
          refreshExpiresIn: 240,
          scope: "scope",
          sessionState: "sessionState");

      dioAdapter.onPost('${UserApi.baseUri}/refresh_token', (server) {
        server.reply(200, newJwtToken);
      });

      final expectedNewTokenEntity =
          JwtTokenEntityBuilder.create(newJwtToken).build();

      //Let's wait for the previous jwt token to expire before calling refresh token helper
      sleep(const Duration(seconds: 3));

      final newToken = await tokenHelper.getOrDownloadToken(forceRefresh: true);

      verify(tokenDao.getToken());
      final savedTokenEntity = verify(tokenDao.saveToken(captureAny))
          .captured
          .single as JwtTokenEntity;
      expect(savedTokenEntity.accessToken, expectedNewTokenEntity.accessToken);
      expect(
          savedTokenEntity.refreshToken, expectedNewTokenEntity.refreshToken);
      expect(newToken, "accessTokenUpdated");
    });
  });
}
