import 'package:dcli/dcli.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:konbit_flutter/core/database/geo_asset_unziper.dart';
import 'package:konbit_flutter/core/helper/checksum_helper.dart';
import 'package:konbit_flutter/core/logger/logger.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();

  String dbFileName = createTempFile(suffix: '.db');
  logger.info('Database file : $dbFileName');
  final dbFileWritten = await unzipGeoAssetDb(dbFileName);

  group(
    'Asset Integrity Test',
    () {
      test('Test DB File Checksum', () async {
        final assetCheckSum = await getDbAssetFileCheckSum();
        final fileCheckSum = sha256Checksum(dbFileWritten.path.toString());
        expect(assetCheckSum, fileCheckSum);
      });

      test('Test DB Bzip File Checksum', () async {
        final assetCheckSum = await sha256ChecksumDbAsset();
        final fileCheckSum = await getDbZipFileCheckSum();
        expect(assetCheckSum, fileCheckSum);
      });

      tearDownAll(
        () async {
          logger.info('Deleting : $dbFileWritten');
          await dbFileWritten.delete();
        },
      );
    },
  );
}
