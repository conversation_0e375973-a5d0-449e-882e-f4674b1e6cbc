import 'package:dio/dio.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:http_mock_adapter/http_mock_adapter.dart';
import 'package:konbit_flutter/core/api/dio_builder.dart';
import 'package:konbit_flutter/core/api/user_api.dart';
import 'package:konbit_flutter/core/exception/app_exception.dart';
import 'package:konbit_flutter/core/model/input/login_info.dart';
import 'package:konbit_flutter/core/model/output/http_konbit_message.dart';
import 'package:konbit_flutter/core/model/output/jwt_token.dart';
import 'package:konbit_flutter/core/model/output/user_dto_info.dart';
import 'package:konbit_flutter/core/model/input/user_subscription.dart';

void main() {
  late Dio dio;
  late DioAdapter dioAdapter;
  late CancelToken cancelToken;

  group(
    'UserApi Test',
    () {
      //setup test
      setUp(
        () {
          dio = DioBuilder.create(retryEnabled: false).build();
          cancelToken = CancelToken();
          dioAdapter = DioAdapter(
              dio: dio,
              matcher: const UrlRequestMatcher(matchMethod: true),
              printLogs: true);
        },
      );

      test(
        'Test User Registration',
        () async {
          final userSub = UserSubscription(
              username: 'doug', email: '<EMAIL>', password: 'hello123');
          final expectedDto = UserDtoInfo(
              username: userSub.username,
              email: userSub.email,
              emailVerified: false,
              enabled: true,
              role: UserRole.regular,
              createdAt: DateTime.now(),
              updatedAt: DateTime.now(),
              lastSeen: DateTime.now());

          dioAdapter.onPost(
            UserApi.baseUri,
            (server) {
              //debugPrint('Server has been reached');
              server.reply(201, expectedDto);
            },
          );

          final userApi = UserApi(dio, cancelToken);

          final response = await userApi.registerUser(userSub);
          expect(response!.statusCode, 201);
          expect(response.data!.username, userSub.username);
        },
      );

      test('Internal Server Error', () async {
        final userSub = UserSubscription(
            username: 'doug', email: '<EMAIL>', password: 'hello123');

        dioAdapter.onPost(
          UserApi.baseUri,
          (server) {
            //debugPrint('Server has been reached');
            server.reply(
                500,
                HttpKonbitMessage(
                    message: 'Internal Server Error',
                    status: KonbitMessageType.InternalError,
                    statusCode: 500));
            //server.reply(500, "Internal error");
          },
        );
        final userApi = UserApi(dio, cancelToken);
        final futureResult = userApi.registerUser(userSub);
        expectLater(futureResult, throwsA(isA<AppException>()));
        futureResult.then((value) {}, onError: (e) {
          expect((e as AppException).httpMessage, isNotNull);
          expect((e).httpMessage!.statusCode, 500);
        });
      });

      test(
        'Login Success Test',
        () async {
          final expectedJwtToken = JwtToken(
              accessToken: 'accessToken',
              refreshToken: 'refreshToken',
              tokenType: 'jwt',
              expiresIn: 0,
              notBeforePolicy: 0,
              refreshExpiresIn: 0,
              scope: 'scope',
              sessionState: 'sessionState');

          dioAdapter.onPost(
            '${UserApi.baseUri}/login',
            (server) {
              server.reply(200, expectedJwtToken);
            },
          );

          final loginInfo =
              LoginInfo(username: 'dessalines', password: 'hello123');

          final userApi = UserApi(dio, cancelToken);
          final Response<JwtToken>? response = await userApi.login(loginInfo);
          expect(response!.statusCode, 200);
          expect(response.data!, expectedJwtToken);
        },
      );

      test("Refresh Token Test", () async {
        final expectedJwtToken = JwtToken(
            accessToken: 'accessTokenUpdate',
            refreshToken: 'refreshTokenUpdate',
            tokenType: 'jwt',
            expiresIn: 0,
            notBeforePolicy: 0,
            refreshExpiresIn: 0,
            scope: 'scopeUpdate',
            sessionState: 'sessionStateUpdate');

        dioAdapter.onPost(
          '${UserApi.baseUri}/refresh_token',
          (server) {
            server.reply(200, expectedJwtToken);
          },
        );

        const token = 'RefreshToken';
        final userApi = UserApi(dio, cancelToken);
        final Response<JwtToken>? response = await userApi.refreshToken(token);
        expect(response!.statusCode, 200);
        expect(response.data!, expectedJwtToken);
      });
    },
  );
}
