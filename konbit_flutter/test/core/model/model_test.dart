import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:test/test.dart';
import 'package:konbit_flutter/core/model/input/user_subscription.dart';

void main() {
  test('test Subscription model', () {
    UserSubscription userSub = UserSubscription(
        username: 'doug', email: '<EMAIL>', password: 'hello123');

    logger.info('UserSubscription: ${userSub.toJson()}');
    var json = {
      'username': 'james',
      'email': '<EMAIL>',
      'password': 'hello123',
    };

    var expectedSub = UserSubscription(
        username: 'james', email: '<EMAIL>', password: 'hello123');
    var sub = UserSubscription.fromJson(json);
    expect(sub, expectedSub,
        reason:
            'expected=\t${expectedSub.toJson()}\r\nactual=\t${sub.toJson()} are not equal');
  });
}
