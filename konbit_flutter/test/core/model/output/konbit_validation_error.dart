import 'package:konbit_flutter/core/model/output/field_error.dart';
import 'package:konbit_flutter/core/model/output/konbit_validation_error.dart';
import 'package:test/test.dart';

void main() {
  List<FieldError> createFieldErrors(int size) => List.generate(
      size,
      (index) => FieldError()
        ..errorCode = "error_code_${index + 1}"
        ..errorDetail = "error_detail ${index + 1}"
        ..params = {});

  test("test konbit validation serialization", skip: true, () {
    final List<FieldError> fieldError1 = createFieldErrors(3);
    final List<FieldError> fieldError2 = createFieldErrors(6);
    final List<FieldError> fieldError3 = createFieldErrors(9);
    final dynamic errors = {
      'first_error': fieldError1.toString() as dynamic,
      'second_error': fieldError2.toString() as dynamic,
      'third_error': fieldError3.toString() as dynamic,
    };

    //expect(errors is Map<String, List<FieldError>>, true);

    final KonbitValidationError validationError =
        KonbitValidationError.fromJson(errors);

    expect(validationError.errorMap.entries.length, 3);
    expect(validationError.errorMap['first_error'], fieldError1);
    expect(validationError.errorMap['second_error'], fieldError2);
    expect(validationError.errorMap['third_error'], fieldError3);
  });
}
