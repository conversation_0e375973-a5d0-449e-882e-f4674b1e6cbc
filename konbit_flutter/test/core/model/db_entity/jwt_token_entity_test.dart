import 'package:flutter/foundation.dart';
import 'package:konbit_flutter/core/model/db_entity/jwt_token_entity.dart';
import 'package:konbit_flutter/core/model/output/jwt_token.dart';
import 'package:test/test.dart';

void main() {
  test("Test Token Expiry", () {
    final jwtToken = JwtToken(
        accessToken: 'accessToken',
        refreshToken: 'refreshToken',
        tokenType: 'JsonWebToken',
        expiresIn: 30,
        notBeforePolicy: 0,
        refreshExpiresIn: 600,
        scope: 'scope',
        sessionState: 'sessionState');

    final jwtTokenEntity = JwtTokenEntity.from(jwtToken,
        tokenMaxLifeTime: Duration(seconds: 60),
        refreshTokenMaxLifetime: Duration(minutes: 10));

    debugPrint(jwtTokenEntity.toString());
    //is token near to 1mns of its lifetime
    expect(jwtTokenEntity.isTokenNearExpiry(), true);
    //is refreshtoken near to 10mns of its lifetime
    expect(jwtTokenEntity.isRefreshTokenNearExpiry(), true);

    expect(jwtTokenEntity.isAccessTokenExpired(), false);
    expect(jwtTokenEntity.isRefreshTokenExpired(), false);
  });
}
