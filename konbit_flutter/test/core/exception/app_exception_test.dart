import 'dart:async';
import 'dart:io';
import 'package:flutter/foundation.dart';

import 'package:konbit_flutter/core/exception/app_exception.dart';
import 'package:test/test.dart';

void main() {
  test('AppException Test Unknown Failure', () {
    AppException appException =
        AppException(exception: const FormatException("Illegal format"));
    expect(appException.kind, ExceptionKind.unknownFailure);
    debugPrint('Unknown failure test');
  });

  test('AppException Test Timeout', () {
    AppException appException =
        AppException(exception: const SocketException('No Network'));
    expect(appException.kind, ExceptionKind.noInternet);
  });

  test('AppException Test Timeout', () {
    AppException appException =
        AppException(exception: TimeoutException('Connection Timeout'));
    expect(appException.kind, ExceptionKind.networkTimeout);
  });
}
