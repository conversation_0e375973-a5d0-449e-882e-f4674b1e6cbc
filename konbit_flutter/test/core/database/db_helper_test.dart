import 'package:konbit_flutter/core/database/db_helper.dart';
import 'package:test/test.dart';

void main() {
  group('test buildSqlSearchPattern', () {
    final Map<String, String> inputsExpected = {
      'Jacmel': 'jacm_l%',
      'Montréal': 'm_ntr_al%',
      'Montréal-Est': 'm_ntr_al%_st%',
      'Port-au-': 'p_rt%au%',
      'port-a-piment': 'p_rt%a%p_m_nt%',
      'Haiti': 'ha_t_%'
    };

    inputsExpected.forEach(
      (input, expected) {
        test('test $input => $expected', () {
          expect(buildSqlSearchPattern(input), expected);
        });
      },
    );
  });
}
