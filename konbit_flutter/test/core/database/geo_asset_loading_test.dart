import 'package:dcli/dcli.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:konbit_flutter/core/database/geo_asset_unziper.dart';
import 'package:konbit_flutter/core/logger/logger.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  test('Test DB assets Load', () async {
    String dbFileName = createTempFile(suffix: '.db');
    logger.info('Database file : $dbFileName');
    final dbFileWritten = await unzipGeoAssetDb(dbFileName);
    await dbFileWritten.delete();
  });
}
