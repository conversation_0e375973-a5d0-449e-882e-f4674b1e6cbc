import 'package:dcli/dcli.dart';
import 'package:konbit_flutter/core/config/konbit_config.dart';
import 'package:konbit_flutter/core/database/db_helper.dart';
import 'package:konbit_flutter/core/database/jwt_token_dao.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/core/model/db_entity/jwt_token_entity.dart';
import 'package:konbit_flutter/core/model/output/jwt_token.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';
import 'package:test/test.dart';

void main() async {
  String dbFileName = createTempFile(suffix: '.db');
  logger.info('Database file : $dbFileName');
  sqfliteFfiInit();
  var databaseFactory = databaseFactoryFfi;

  final Database db = await databaseFactory.openDatabase(dbFileName);
  await db.execute(tblSqlScript);

  final JwtTokenDao tokenDao = JwtTokenDao(database: db);

  group(
    'JwtTokenDao Test',
    () {
      test('Create Token Test', () async {
        final jwtToken = JwtToken(
            accessToken: 'accessToken',
            refreshToken: 'refreshToken',
            tokenType: 'JsonWebToken',
            expiresIn: 300,
            notBeforePolicy: 0,
            refreshExpiresIn: 600,
            scope: 'scope',
            sessionState: 'sessionState');

        final jwtTokenEntity = JwtTokenEntity.from(jwtToken,
            tokenMaxLifeTime: tokenThreshold,
            refreshTokenMaxLifetime: refreshTokenThreshold);

        await tokenDao.saveToken(jwtTokenEntity);

        final JwtTokenEntity? token = await tokenDao.getToken();
        expect(token != null, true);
        expect(token!.accessToken, "accessToken");
      });

      test('Update Token Test', () async {
        final jwtToken = JwtToken(
            accessToken: 'accessTokenUpdated',
            refreshToken: 'refreshTokenUpdated',
            tokenType: 'JsonWebToken',
            expiresIn: 180,
            notBeforePolicy: 0,
            refreshExpiresIn: 400,
            scope: 'scopeUpdated',
            sessionState: 'sessionStateUpdated');

        final jwtTokenEntity = JwtTokenEntity.from(jwtToken,
            tokenMaxLifeTime: tokenThreshold,
            refreshTokenMaxLifetime: refreshTokenThreshold);
        await tokenDao.saveToken(jwtTokenEntity);

        final JwtTokenEntity? token = await tokenDao.getToken();
        expect(token != null, true);
        expect(token!.accessToken, "accessTokenUpdated");
      });
    },
  );
}
