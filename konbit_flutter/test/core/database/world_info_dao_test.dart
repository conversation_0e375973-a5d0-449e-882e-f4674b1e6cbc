import 'package:dcli/dcli.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:konbit_flutter/core/database/geo_asset_unziper.dart';
import 'package:konbit_flutter/core/database/world_info_dao.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/core/model/db_entity/city.dart';
import 'package:konbit_flutter/core/model/db_entity/country.dart';
import 'package:konbit_flutter/core/model/db_entity/state.dart';
import 'package:sqflite_common_ffi/sqflite_ffi.dart';

void main() async {
  TestWidgetsFlutterBinding.ensureInitialized();

  String dbFileName = createTempFile(suffix: '.db');
  final geoDbFile = await unzipGeoAssetDb(dbFileName);
  sqfliteFfiInit();
  var databaseFactory = databaseFactoryFfi;

  final geoDb = await databaseFactory.openDatabase(geoDbFile.absolute.path);
  logger.info('Database file : $dbFileName');

  WorldInfoDao worldInfoDao = WorldInfoDao(database: geoDb);

  group('WorldInfoDao test', () {
    test('find City', () async {
      final List<City> cities = await worldInfoDao.cityLookup('Port-au-Prince');
      final List<String> cityNames = cities.map((city) => city.name).toList();
      logger.info('City List : $cityNames');

      expect('Port-au-Prince', isIn(cityNames));
    });

    test('find City in State', () async {
      final List<City> cities =
          await worldInfoDao.cityLookup('Montreal', stateName: 'Quebec');
      final List<String> cityNames = cities.map((city) => city.name).toList();
      logger.info('City List : $cityNames');

      expect('Montréal', isIn(cityNames));
    });

    test('find City in Country', () async {
      final List<City> cities =
          await worldInfoDao.cityLookup('Paris', countryName: 'France');
      final List<String> cityNames = cities.map((city) => city.name).toList();
      logger.info('City List : $cityNames');

      expect('Paris', isIn(cityNames));
    });

    test('find City in States And Country', () async {
      final List<City> cities = await worldInfoDao.cityLookup('Paris',
          stateName: 'Ile de France', countryName: 'France');
      final List<String> cityNames = cities.map((city) => city.name).toList();
      logger.info('City List : $cityNames');

      expect('Paris', isIn(cityNames));
    });

    test('find State', () async {
      final List<State> states = await worldInfoDao.stateLookup('New York');
      final List<String> stateNames = states.map((city) => city.name).toList();
      logger.info('State List : $stateNames');

      expect('New York', isIn(stateNames));
    });

    test('find State in Country', () async {
      final List<State> states =
          await worldInfoDao.stateLookup('Nippes', 'Haiti');
      final List<String> stateNames = states.map((city) => city.name).toList();
      logger.info('State List : $stateNames');

      expect('Nippes', isIn(stateNames));
    });

    test('find Country', () async {
      final List<Country> countries = await worldInfoDao.countryLookup('Haiti');
      final List<String> countryNames =
          countries.map((city) => city.name).toList();
      logger.info('Country List : $countryNames');

      expect('Haiti', isIn(countryNames));
    });
  });

  logger.info('Deleting db file : $dbFileName');
  geoDbFile.deleteSync();
}
