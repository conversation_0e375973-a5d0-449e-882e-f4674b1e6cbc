name: 'konbit_flutter'
version: 0.1.0
description: Konbit Flutter Project.
publish_to: none

environment:
  sdk: '>=3.4.3 <4.0.0'

dependencies:
  archive: ^4.0.7
  async: ^2.12.0
  beamer: '^1.7.0'
  built_value: ^8.10.1
  cached_network_image: ^3.4.1
  camera: ^0.11.1
  camerawesome: ^2.5.0
  collection: ^1.19.1
  crypto: '^3.0.6'
  cupertino_icons: '^1.0.8'
  dio: ^5.8.0+1
  dio_smart_retry: '^7.0.1'
  draggable_float_widget: ^0.1.0
  flutter:
    sdk: flutter
  flutter_cache_manager: ^3.4.1
  flutter_easy_dialogs: ^4.0.3
  flutter_spinkit: '^5.2.1'
  flutter_swipe_button: '^2.1.3'
  flutter_typeahead: '^5.2.0'
  geo_resources:
    path: '../geo_resources/'
  geolocator: ^14.0.1
  google_maps_flutter: ^2.12.3
  google_maps_flutter_android: ^2.16.1
  google_maps_flutter_platform_interface: ^2.12.1
  hugeicons: ^0.0.11
  image_picker: ^1.1.2
  json_annotation: '^4.9.0'
  jwt_decoder: '^2.0.1'
  openapi_generator_annotations: ^6.1.0
  path: ^1.9.1
  path_provider: ^2.1.5
  provider: '^6.1.4'
  pubspec_dependency_sorter: '^1.0.5'
  quickalert: '^1.1.0'
  quiver: '^3.2.2'
  reactive_date_time_picker: ^5.0.0
  reactive_dropdown_search: ^6.0.0-pre4
  reactive_forms: ^18.0.1
  reactive_phone_form_field: ^5.0.2
  shared_preferences: ^2.5.2
  sheet: '^1.0.0'
  sqflite: ^2.4.2
  sqflite_common_ffi: ^2.3.5
  talker_dio_logger: ^4.9.0
  talker_flutter: ^4.9.0
  toastification: ^3.0.1
  uuid: '^4.5.1'

dev_dependencies:
  build_runner: ^2.5.2
  built_value_generator: '^8.10.1'
  dcli: '^7.0.5'
  flutter_lints: ^6.0.0
  flutter_test:
    sdk: flutter
  http_mock_adapter: '^0.6.1'
  json_serializable: '^6.8.0'
  mockito: ^5.4.5
  openapi_generator: ^6.1.0
  test: ^1.25.15

assets:
  - 'packages/geo_resources/assets/world_data.db.bz2'

flutter:
  uses-material-design: true
