import 'package:shared_preferences/shared_preferences.dart';

import '../config/konbit_config.dart';

const protocolKey = 'httpScheme';
const serverKey = 'server';
const portKey = 'port';

Future<void> loadKonbitConfig() async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();

  final konbitCfgInstance = KonbitConfig.getConfig();
  konbitCfgInstance.httpScheme =
      prefs.getString(protocolKey) ?? konbitCfgInstance.httpScheme;
  konbitCfgInstance.server =
      prefs.getString(serverKey) ?? konbitCfgInstance.server;
  konbitCfgInstance.port = prefs.getInt(portKey) ?? konbitCfgInstance.port;
}

Future<void> updateKonbitConfig() async {
  final SharedPreferences prefs = await SharedPreferences.getInstance();

  final konbitCfgInstance = KonbitConfig.getConfig();
  prefs.setString(protocolKey, konbitCfgInstance.httpScheme);
  prefs.setString(serverKey, konbitCfgInstance.server);
  prefs.setInt(portKey, konbitCfgInstance.port);
}
