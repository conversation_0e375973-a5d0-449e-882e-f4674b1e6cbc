import 'package:dio/dio.dart';
import 'package:jwt_decoder/jwt_decoder.dart';
import 'package:konbit_flutter/core/database/jwt_token_dao.dart';
import 'package:konbit_flutter/core/exception/app_exception.dart';
import 'package:konbit_flutter/core/model/db_entity/jwt_token_entity.dart';
import 'package:konbit_flutter/core/model/db_entity/jwt_token_entity_builder.dart';
import 'package:konbit_flutter/core/model/output/http_konbit_message.dart';
import 'package:konbit_flutter/core/model/user_auth_data.dart';

import '../api/dio_builder.dart';
import '../api/interceptor/http_interceptor.dart';
import '../api/user_api.dart';
import '../api/user_profile_api.dart';
import '../config/konbit_config.dart';
import '../database/db_helper.dart';
import '../logger/logger.dart';
import '../model/output/user_profile_dto.dart';

class TokenHelper {
  TokenHelper(
      {required JwtTokenDao tokenDao,
      required Dio dio,
      CancelToken? cancelToken})
      : _jwtTokenDao = tokenDao,
        _dio = dio,
        _cancelToken = cancelToken;

  final JwtTokenDao _jwtTokenDao;
  final Dio _dio;
  final CancelToken? _cancelToken;

  /// always return a valid usable token stored from a local sqlite3 database
  /// or download a new valid ones by refresh it from Konbit server if refresh token exists
  /// Return null if no valid token is found
  ///
  /// Can throw Exception if it failed to refresh the jwt token
  Future<String?> getOrDownloadToken({bool forceRefresh = false}) async {
    JwtTokenEntity? tokenEntity = await _jwtTokenDao.getToken();
    if (tokenEntity == null) {
      return null;
    }

    if (!forceRefresh && _isTokenInvalid(tokenEntity)) {
      return null;
    }

    final UserApi userApi = UserApi(_dio, _cancelToken ?? CancelToken());

    if (tokenEntity.isRefreshTokenExpired()) {
      // We can't refresh access token with an expired refresh token.
      _jwtTokenDao.deleteToken();
      return null;
    }

    if (forceRefresh || tokenEntity.isTokenNearExpiry()) {
      try {
        final response = await userApi.refreshToken(tokenEntity.refreshToken);
        final jwtToken = response!.data!;
        final newTokenEntity = JwtTokenEntityBuilder.create(jwtToken).build();
        _jwtTokenDao.saveToken(newTokenEntity);
        return newTokenEntity.accessToken;
      } on Exception catch (ex, _) {
        //If we encounter errors during Token download or persistence,
        // we return the current access token if it's still valid
        if (!tokenEntity.isAccessTokenExpired()) {
          return tokenEntity.accessToken;
        }
        logger.error('failed to proceed with token refresh : ${ex.toString()}');
        rethrow;
      }
    }

    return tokenEntity.accessToken;
  }

  bool _isTokenInvalid(JwtTokenEntity tokenEntity) {
    return (tokenEntity.isAccessTokenExpired() ||
            tokenEntity.isTokenNearExpiry()) &&
        tokenEntity.isRefreshTokenExpired();
  }
}

bool _isInvalidTokenError(AppException appException) {
  if (appException.httpMessage == null) {
    return false;
  }

  final messageType = appException.httpMessage!.status;

  return messageType == KonbitMessageType.InvalidJwtToken;
}

String getUsernameFromToken(String token) {
  final claims = JwtDecoder.decode(token);
  String username = claims['preferred_username'];
  return username;
}

/// Retrieve the JWT Token if stored locally or from Konbit Server.
/// It performs token refresh if it is about to expire
///
/// [onProfileMissing(String token)] call back with token
/// []
Future<UserAuthData> processAndRetrieveToken(
    {required CancelToken cancelToken,
    void Function(String)? onValidToken,
    void Function()? onTokenMissing,
    void Function(String)? onProfileMissing,
    void Function(Exception)? onError,
    bool forceRefresh = false}) async {
  logger.debug('processAndRetrieveToken running');
  final database = await getDatabase();

  final tokenDao = JwtTokenDao(database: database);
  final dio =
      DioBuilder.create().baseUrl(KonbitConfig.getConfig().getUrl()).build();
  final tokenHelper =
      TokenHelper(tokenDao: tokenDao, dio: dio, cancelToken: cancelToken);

  UserAuthData authData = UserAuthData();
  String? token;

  try {
    token = await tokenHelper.getOrDownloadToken(forceRefresh: forceRefresh);
  } on Exception catch (ex, _) {
    //If we encounter 'InvalidJwtToken' error, we don't throw error
    //we delete the inactive token from our local sqlite3 database,
    // and return null to notify that there is no token available
    //that is to force the user to log back in
    if (ex is AppException && _isInvalidTokenError(ex)) {
      //authData.token = null;
      await tokenDao.deleteToken();
      return authData;
    }

    if (onError != null) {
      onError(ex);
    }

    rethrow;
  }

  if (token == null) {
    if (onTokenMissing != null) {
      onTokenMissing();
    }
    return authData;
  }

  if (onValidToken != null) {
    onValidToken(token);
  }

  final accessTokenInterceptor = AccessTokenInterceptor.getSingleInstance();
  accessTokenInterceptor.accessToken = token;
  dio.interceptors.add(accessTokenInterceptor);

  authData.token = token;

  final profileApi = UserProfileApi(dio, cancelToken);
  try {
    final Response<UserProfileDto>? response = await profileApi.getMyProfile();
    authData.userProfile = response!.data;
  } on Exception catch (ex) {
    if (ex is AppException &&
        ex.httpMessage != null &&
        ex.httpMessage!.status == KonbitMessageType.NotFound) {
      if (onProfileMissing != null) {
        onProfileMissing(token);
      }

      return authData;
    }

    if (onError != null) {
      onError(ex);
    }

    rethrow;
  }

  return authData;
}
