import 'dart:io';

import 'package:crypto/crypto.dart';
import 'package:flutter/services.dart';

String sha256Checksum(String file) {
  final dbFile = File.fromUri(Uri.file(file));
  final bytes = dbFile.readAsBytesSync();

  final digest = sha256.convert(bytes);
  return digest.toString();
}

Future<String> getDbAssetFileCheckSum() async {
  return await rootBundle
      .loadString('packages/geo_resources/assets/world_data.db.sha256');
}

Future<String> getDbZipFileCheckSum() async {
  return await rootBundle
      .loadString('packages/geo_resources/assets/world_data.bz2.sha256');
}

Future<String> sha256ChecksumDbAsset() async {
  final ByteData byteData =
      await rootBundle.load('packages/geo_resources/assets/world_data.db.bz2');
  final digest = sha256.convert(byteData.buffer.asInt8List().toList());
  return digest.toString();
}

/*
  final ByteData byteData =
      await rootBundle.load('packages/geo_resources/assets/world_data.db.bz2');
  BZip2Decoder bZip2Decoder = BZip2Decoder();
 */
