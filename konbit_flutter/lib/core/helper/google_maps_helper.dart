import 'package:google_maps_flutter/google_maps_flutter.dart';

const double defaultZoom = 12.0;

Future<void> moveCameraToPosition(
    GoogleMapController? mapController, LatLng position, double zoom) async {
  final cameraPos = CameraPosition(zoom: zoom, target: position);
  if (mapController == null) {
    await Future.delayed(Duration(milliseconds: 100));
  }
  mapController?.animateCamera(CameraUpdate.newCameraPosition(cameraPos));
}
