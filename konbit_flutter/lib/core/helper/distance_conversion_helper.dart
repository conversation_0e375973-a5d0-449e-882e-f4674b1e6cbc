enum DistanceUnit { kilometer, miles }

String getReadableDistance(double distanceMeter, DistanceUnit toUnit) {
  switch (toUnit) {
    case DistanceUnit.kilometer:
      return distanceMeter < 1000.0
          ? '${distanceMeter.floor()}m'
          : '${(distanceMeter / 1000.0).floor().toString()}km';
    case DistanceUnit.miles:
      return distanceMeter < 1000.0
          ? '${(distanceMeter * 3.28084).floor()}ft'
          : '${(distanceMeter / 1609.34).floor().toString()}miles';
  }
}
