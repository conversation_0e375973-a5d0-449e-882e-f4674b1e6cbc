import 'package:dio/dio.dart';
import 'package:konbit_flutter/core/api/profile_picture_api.dart';
import 'package:konbit_flutter/core/api/user_profile_api.dart';

import '../api/dio_builder.dart';
import '../api/interceptor/http_interceptor.dart';
import '../api/user_api.dart';
import '../config/konbit_config.dart';

class UserProfileApiBuilder {
  UserProfileApiBuilder._(UserProfileApi userProfileApi)
      : _userProfileApi = userProfileApi;

  final UserProfileApi _userProfileApi;

  factory UserProfileApiBuilder.withoutAuthentication(CancelToken cancelToken) {
    final userProfileApi = UserProfileApi(
        DioBuilder.create().baseUrl(KonbitConfig.getConfig().getUrl()).build(),
        cancelToken);
    return UserProfileApiBuilder._(userProfileApi);
  }

  factory UserProfileApiBuilder.withAuthentication(CancelToken cancelToken) {
    final userProfileApi = UserProfileApi(
        DioBuilder.create()
            .baseUrl(KonbitConfig.getConfig().getUrl())
            .interceptor(AccessTokenInterceptor.getSingleInstance())
            .build(),
        cancelToken);
    return UserProfileApiBuilder._(userProfileApi);
  }

  UserProfileApi build() {
    return _userProfileApi;
  }
}

class UserApiBuilder {
  UserApiBuilder._(UserApi userApi) : _userApi = userApi;

  final UserApi _userApi;

  factory UserApiBuilder.withoutAuthentication(CancelToken cancelToken) {
    final userApi = UserApi(createDioWithAccessTokeInterceptor(), cancelToken);
    return UserApiBuilder._(userApi);
  }

  factory UserApiBuilder.withAuthentication(CancelToken cancelToken) {
    final userApi = UserApi(createDioWithAccessTokeInterceptor(), cancelToken);
    return UserApiBuilder._(userApi);
  }

  UserApi build() {
    return _userApi;
  }
}

class ProfilePictureApiBuilder {
  ProfilePictureApiBuilder._(ProfilePictureApi profilePictureApi)
      : _profilePictureApi = profilePictureApi;

  final ProfilePictureApi _profilePictureApi;

  factory ProfilePictureApiBuilder.withAuthentication(CancelToken cancelToken) {
    final profilePictureApi =
        ProfilePictureApi(createDioWithAccessTokeInterceptor(), cancelToken);
    return ProfilePictureApiBuilder._(profilePictureApi);
  }

  ProfilePictureApi build() {
    return _profilePictureApi;
  }
}

Dio createDioWithAccessTokeInterceptor() {
  return DioBuilder.create()
      .baseUrl(KonbitConfig.getConfig().getUrl())
      .interceptor(AccessTokenInterceptor.getSingleInstance())
      .build();
}
