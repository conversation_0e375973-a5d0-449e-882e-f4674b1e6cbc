import 'package:json_annotation/json_annotation.dart';
import 'package:quiver/core.dart';

part 'field_error.g.dart';

@JsonSerializable(includeIfNull: false)
class FieldError {
  @JsonKey(name: 'error_code')
  late String errorCode;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'error_detail')
  String? errorDetail;

  @J<PERSON><PERSON>ey(name: 'params')
  Map<String, dynamic>? params;

  FieldError();

  Map<String, dynamic> toJson() => _$FieldErrorToJson(this);

  factory FieldError.fromJson(Map<String, dynamic> json) =>
      _$FieldErrorFromJson(json);

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is FieldError &&
          runtimeType == other.runtimeType &&
          errorCode == other.errorCode &&
          errorDetail == other.errorDetail &&
          params == other.params;

  @override
  int get hashCode =>
      hashObjects([errorCode.hashCode, errorDetail.hashCode, params.hashCode]);

  @override
  String toString() {
    return 'FieldError{errorCode: $errorCode, errorDetail: $errorDetail, params: $params}';
  }
}
