import 'package:json_annotation/json_annotation.dart';
import 'package:quiver/core.dart';
part 'http_konbit_message.g.dart';

enum KonbitMessageType {
  ActionComplete,
  EmailNotVerified,
  UserDisabled,
  InvalidCredentials,
  InvalidJwtToken,
  DuplicateProfile,
  UsernameAlreadyExist,
  EmailAlreadyExist,
  InternalError,
  NotAuthorized,
  NotFound,
  InvalidContent,
  UnexpectedFailure,
  GeocodingLookupError
}

@JsonSerializable()
class HttpKonbitMessage {
  @JsonKey(name: 'status_code', required: true)
  final int statusCode;
  final KonbitMessageType status;
  final String message;

  HttpKonbitMessage(
      {required this.message, required this.status, required this.statusCode});

  Map<String, dynamic> toJson() => _$HttpKonbitMessageToJson(this);

  factory HttpKonbitMessage.fromJson(Map<String, dynamic> json) =>
      _$HttpKonbitMessageFromJson(json);

  @override
  bool operator ==(Object other) {
    if (other is! HttpKonbitMessage) {
      return false;
    }

    return statusCode == other.statusCode &&
        status == other.status &&
        message == other.message;
  }

  @override
  int get hashCode =>
      hashObjects([statusCode.hashCode, status.hashCode, message.hashCode]);
}
