import 'package:konbit_flutter/core/model/output/field_error.dart';

class KonbitValidationError {
  final Map<String, List<FieldError>> errorMap;

  KonbitValidationError(Map<String, dynamic> errors)
      : errorMap = errors
            .map((key, value) => MapEntry(key, value as List<FieldError>));

  factory KonbitValidationError.fromJson(Map<String, dynamic> errors) {
    final fieldErrorMap = <String, List<FieldError>>{};
    for (final fieldErrorEntries in errors.entries) {
      final fieldName = fieldErrorEntries.key;
      final fieldErrors = fieldErrorEntries.value as List<dynamic>;
      final errorList = List.of(<FieldError>[], growable: true);
      for (final fieldErrorEntry in fieldErrors) {
        final fieldError = FieldError.fromJson(fieldErrorEntry);
        errorList.add(fieldError);
      }
      fieldErrorMap.putIfAbsent(fieldName, () => errorList);
    }

    return KonbitValidationError(fieldErrorMap);
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is KonbitValidationError &&
          runtimeType == other.runtimeType &&
          errorMap == other.errorMap;

  @override
  int get hashCode => errorMap.hashCode;
}
