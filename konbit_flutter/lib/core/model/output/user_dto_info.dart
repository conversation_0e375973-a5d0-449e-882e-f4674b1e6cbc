import 'package:json_annotation/json_annotation.dart';
import 'package:quiver/core.dart';
part 'user_dto_info.g.dart';

enum UserRole { regular, admin }

@JsonSerializable()
class UserDtoInfo {
  final String username;
  final String email;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'email_verified')
  final bool emailVerified;
  final bool enabled;
  final UserRole role;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'created_at')
  final DateTime createdAt;
  @<PERSON>son<PERSON><PERSON>(name: 'updated_at')
  final DateTime updatedAt;
  @Json<PERSON>ey(name: 'last_seen')
  final DateTime lastSeen;

  UserDtoInfo(
      {required this.username,
      required this.email,
      required this.emailVerified,
      required this.enabled,
      required this.role,
      required this.createdAt,
      required this.updatedAt,
      required this.lastSeen});

  Map<String, dynamic> toJson() => _$UserDtoInfoToJson(this);

  factory UserDtoInfo.fromJson(Map<String, dynamic> json) =>
      _$UserDtoInfoFromJson(json);

  @override
  bool operator ==(Object other) {
    if (other is! UserDtoInfo) {
      return false;
    }

    return username == other.username &&
        email == other.email &&
        emailVerified == other.emailVerified &&
        enabled == other.enabled &&
        role == other.role &&
        createdAt == other.createdAt &&
        updatedAt == other.updatedAt &&
        lastSeen == other.lastSeen;
  }

  @override
  int get hashCode => hashObjects([
        username.hashCode,
        email.hashCode,
        emailVerified.hashCode,
        enabled.hashCode,
        role.hashCode,
        createdAt.hashCode,
        updatedAt.hashCode,
        lastSeen.hashCode
      ]);
}
