import 'package:json_annotation/json_annotation.dart';

part 'user_profile_search.g.dart';

@JsonSerializable(includeIfNull: false)
class UserProfileSummary {
  UserProfileSummary();

  @Json<PERSON>ey(name: 'city')
  String? city;

  @Json<PERSON>ey(name: 'main_occupation')
  String? mainOccupation;

  @Json<PERSON>ey(name: 'skills')
  List<String>? skills;

  @Json<PERSON>ey(name: 'username')
  String? username;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'bio')
  String? bio;

  @JsonKey(name: 'country_code')
  String? countryCode;

  @JsonKey(name: 'distance')
  double? distance;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'education')
  String? education;

  @JsonKey(name: 'locality_1')
  String? locality1;

  @Json<PERSON><PERSON>(name: 'other_occupation')
  String? otherOccupation;

  @JsonKey(name: 'pic_url')
  String? picUrl;

  @JsonKey(name: 'row_number')
  int? rowNumber;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'zip_code')
  String? zipCode;

  @Json<PERSON>ey(name: 'latitude')
  double latitude = 0.0;

  @Json<PERSON>ey(name: 'longitude')
  double longitude = 0.0;

  Map<String, dynamic> toJson() => _$UserProfileSummaryToJson(this);

  factory UserProfileSummary.fromJson(Map<String, dynamic> json) =>
      _$UserProfileSummaryFromJson(json);
}
