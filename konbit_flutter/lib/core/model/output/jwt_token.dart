import 'package:json_annotation/json_annotation.dart';
import 'package:quiver/core.dart';
part 'jwt_token.g.dart';

@JsonSerializable()
class JwtToken {
  @Json<PERSON>ey(name: 'access_token')
  final String accessToken;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'refresh_token')
  final String refreshToken;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'token_type')
  final String tokenType;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'expires_in')
  final int expiresIn;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'not-before-policy')
  final int notBeforePolicy;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'refresh_expires_in')
  final int refreshExpiresIn;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'scope')
  final String scope;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'session_state')
  final String sessionState;

  JwtToken(
      {required this.accessToken,
      required this.refreshToken,
      required this.tokenType,
      required this.expiresIn,
      required this.notBeforePolicy,
      required this.refreshExpiresIn,
      required this.scope,
      required this.sessionState});

  Map<String, dynamic> toJson() => _$JwtTokenToJson(this);

  factory JwtToken.fromJson(Map<String, dynamic> json) =>
      _$JwtTokenFromJson(json);

  @override
  bool operator ==(Object other) {
    if (other is! JwtToken) {
      return false;
    }

    return accessToken == other.accessToken &&
        refreshToken == other.refreshToken &&
        tokenType == other.tokenType &&
        expiresIn == other.expiresIn &&
        notBeforePolicy == other.notBeforePolicy &&
        refreshExpiresIn == other.refreshExpiresIn &&
        scope == other.scope &&
        sessionState == other.sessionState;
  }

  @override
  int get hashCode => hashObjects([
        accessToken.hashCode,
        refreshToken.hashCode,
        tokenType.hashCode,
        expiresIn.hashCode,
        notBeforePolicy.hashCode,
        refreshExpiresIn.hashCode,
        scope.hashCode,
        sessionState.hashCode
      ]);
}
