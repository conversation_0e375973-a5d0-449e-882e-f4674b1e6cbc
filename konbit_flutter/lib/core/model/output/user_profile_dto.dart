import 'package:json_annotation/json_annotation.dart';
part 'user_profile_dto.g.dart';

@JsonSerializable()
class UserProfileDto {
  UserProfileDto();

  @Json<PERSON>ey(name: 'city')
  String? city;

  @Json<PERSON>ey(name: 'country_code')
  String? countryCode;

  @Json<PERSON><PERSON>(name: 'created_at')
  DateTime? createdAt;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'latitude')
  double? latitude;

  @JsonKey(name: 'locality_1')
  String? locality1;

  @Json<PERSON>ey(name: 'longitude')
  double? longitude;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'main_occupation')
  String? mainOccupation;

  @Json<PERSON><PERSON>(name: 'skills')
  List<String>? skills;

  @Json<PERSON>ey(name: 'street_address')
  String? streetAddress;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'updated_at')
  DateTime? updatedAt;

  @Json<PERSON>ey(name: 'username')
  String? username;

  @<PERSON>son<PERSON><PERSON>(name: 'visible')
  bool? visible;

  @<PERSON>son<PERSON><PERSON>(name: 'zip_code')
  String? zipCode;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'address_line_2')
  String? addressLine2;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'bio')
  String? bio;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'birthdate')
  int? birthDate;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'birthmonth')
  int? birthMonth;

  @JsonKey(name: 'birthyear')
  int? birthYear;

  @JsonKey(name: 'education')
  String? education;

  @JsonKey(name: 'first_name')
  String? firstName;

  @JsonKey(name: 'last_name')
  String? lastName;

  @JsonKey(name: 'locality_2')
  String? locality2;

  @JsonKey(name: 'middle_name')
  String? middleName;

  @JsonKey(name: 'nationality')
  String? nationality;

  @JsonKey(name: 'phone')
  String? phone;

  @JsonKey(name: 'profile_pic')
  String? profilePic;

  @JsonKey(name: 'secondary_occupation')
  String? secondaryOccupation;

  Map<String, dynamic> toJson() => _$UserProfileDtoToJson(this);

  factory UserProfileDto.fromJson(Map<String, dynamic> json) =>
      _$UserProfileDtoFromJson(json);
}
