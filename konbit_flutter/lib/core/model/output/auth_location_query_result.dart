import 'package:json_annotation/json_annotation.dart';
import 'package:konbit_flutter/core/model/output/user_profile_search.dart';

part 'auth_location_query_result.g.dart';

@JsonSerializable()
class AuthLocationQueryResult {
  AuthLocationQueryResult();

  @Json<PERSON>ey(name: 'total_items')
  int? totalItems;

  @Json<PERSON>ey(name: 'next')
  String? next;

  @JsonKey(name: 'prev')
  String? prev;

  @<PERSON>son<PERSON>ey(name: 'profiles')
  List<UserProfileSummary> profiles = List.empty(growable: true);

  Map<String, dynamic> toJson() => _$AuthLocationQueryResultToJson(this);

  factory AuthLocationQueryResult.fromJson(Map<String, dynamic> json) =>
      _$AuthLocationQueryResultFromJson(json);
}
