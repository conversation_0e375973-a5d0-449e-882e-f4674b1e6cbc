import 'package:json_annotation/json_annotation.dart';
import 'package:quiver/core.dart';
part 'user_subscription.g.dart';

@JsonSerializable()
class UserSubscription {
  final String username;
  final String email;
  final String password;

  UserSubscription(
      {required this.username, required this.email, required this.password});

  Map<String, dynamic> toJson() => _$UserSubscriptionToJson(this);

  factory UserSubscription.fromJson(Map<String, dynamic> json) =>
      _$UserSubscriptionFromJson(json);

  @override
  bool operator ==(Object other) {
    if (other is! UserSubscription) {
      return false;
    }

    return username == other.username &&
        email == other.email &&
        password == other.password;
  }

  @override
  int get hashCode =>
      hashObjects([username.hashCode, email.hashCode, password.hashCode]);
}
