import 'package:json_annotation/json_annotation.dart';
import 'package:quiver/core.dart';
part 'login_info.g.dart';

@JsonSerializable()
class LoginInfo {
  final String username;
  final String password;

  LoginInfo({required this.username, required this.password});

  Map<String, dynamic> toJson() => _$LoginInfoToJson(this);

  factory LoginInfo.fromJson(Map<String, dynamic> json) =>
      _$LoginInfoFromJson(json);

  @override
  bool operator ==(Object other) {
    if (other is! LoginInfo) {
      return false;
    }

    return username == other.username && password == other.password;
  }

  @override
  int get hashCode => hash2(username.hashCode, password.hashCode);
}
