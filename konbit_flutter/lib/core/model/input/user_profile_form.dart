import 'package:json_annotation/json_annotation.dart';
part 'user_profile_form.g.dart';

@JsonSerializable(includeIfNull: false)
class UserProfileForm {
  @Json<PERSON>ey(name: 'first_name')
  String? firstname;

  @<PERSON><PERSON><PERSON><PERSON>(name: 'middle_name')
  String? middlename;

  @J<PERSON><PERSON><PERSON>(name: 'last_name')
  String? lastname;

  @Json<PERSON><PERSON>(name: 'birth_date')
  int? birthDate;

  @JsonKey(name: 'birth_month')
  int? birthMonth;

  @Json<PERSON><PERSON>(name: 'birth_year')
  int? birthYear;

  String? nationality;
  String? education;

  @Json<PERSON>ey(name: 'main_occupation')
  String? mainOccupation;

  @J<PERSON><PERSON><PERSON>(name: 'second_occupation')
  String? secondOccupation;
  List<String>? skills;

  @Json<PERSON>ey(name: 'street_address')
  String? streetAddress;

  @Json<PERSON>ey(name: 'address_2')
  String? address2;
  String? city;
  @JsonKey(name: 'locality_1')
  String? locality1;
  @<PERSON><PERSON><PERSON><PERSON>(name: 'locality_2')
  String? locality2;
  String? country;

  @Json<PERSON>ey(name: 'zip_code')
  String? zipCode;

  String? phone;

  @J<PERSON><PERSON><PERSON>(name: 'profile_pic')
  String? profilePic;

  String? bio;

  UserProfileForm();

  Map<String, dynamic> toJson() => _$UserProfileFormToJson(this);

  factory UserProfileForm.fromJson(Map<String, dynamic> json) =>
      _$UserProfileFormFromJson(json);
}
