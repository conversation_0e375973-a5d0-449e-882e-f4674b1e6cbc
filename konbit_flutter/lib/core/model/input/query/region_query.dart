import 'package:json_annotation/json_annotation.dart';

import 'cursor_direction.dart';
part 'region_query.g.dart';

@JsonSerializable(includeIfNull: false)
class RegionQuery {
  final String city;
  final String country;
  String? occupation;
  String? cursor;
  CursorDirection? direction;
  int? limit;

  RegionQuery(
      {required this.city,
      required this.country,
      this.occupation,
      this.cursor,
      this.direction,
      this.limit});

  Map<String, dynamic> toJson() => _$RegionQueryToJson(this);

  static RegionQuery fromJson(Map<String, dynamic> json) =>
      _$RegionQueryFromJson(json);
}
