import 'package:json_annotation/json_annotation.dart';
import 'cursor_direction.dart';
part 'location_query.g.dart';

@JsonSerializable(includeIfNull: false)
class LocationQuery {
  LocationQuery(
      {required this.latitude,
      required this.longitude,
      this.occupation,
      this.cursor,
      this.direction,
      this.maxDistance,
      this.limit});
  final double latitude;

  final double longitude;
  String? occupation;

  String? cursor;

  CursorDirection? direction;

  @JsonKey(name: r'max_distance')
  int? maxDistance;

  int? limit;

  Map<String, dynamic> toJson() => _$LocationQueryToJson(this);

  static LocationQuery fromJson(Map<String, dynamic> json) =>
      _$LocationQueryFromJson(json);
}
