import 'package:flutter/foundation.dart';
import 'package:konbit_flutter/core/model/output/user_profile_dto.dart';

class UserAuthData with ChangeNotifier, DiagnosticableTreeMixin {
  UserAuthData({String? token, UserProfileDto? userProfile})
      : _token = token,
        _userProfile = userProfile;

  String? _token;
  UserProfileDto? _userProfile;

  String? get token => _token;

  set token(String? value) {
    _token = value;
    notifyListeners();
  }

  factory UserAuthData.from(UserAuthData userAuthData) {
    return UserAuthData(
        token: userAuthData.token, userProfile: userAuthData.userProfile);
  }

  bool hasProfile() => userProfile != null;

  bool isAuthenticated() => token != null;

  bool isProfileActive() =>
      hasProfile() && userProfile!.visible != null && userProfile!.visible!;

  void updateUserData(UserAuthData userAuthData) {
    token = userAuthData.token;
    userProfile = userAuthData.userProfile;
    notifyListeners();
  }

  UserProfileDto? get userProfile => _userProfile;

  set userProfile(UserProfileDto? profile) {
    _userProfile = profile;
    notifyListeners();
  }
}
