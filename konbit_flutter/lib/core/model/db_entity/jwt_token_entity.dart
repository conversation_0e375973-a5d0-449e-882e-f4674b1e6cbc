import 'package:konbit_flutter/core/model/output/jwt_token.dart';

class JwtTokenEntity {
  late String accessToken = '';
  late String refreshToken = '';
  late DateTime accessTokenExpiry = DateTime.timestamp();
  late DateTime refreshTokenExpiry = DateTime.timestamp();
  late DateTime tokenCreationDate = DateTime.timestamp();
  final Duration _tokenLifeCycle;
  final Duration _refreshTokenLifeCycle;
  static const String TOKEN_NAME = 'JWT';

  JwtTokenEntity({required tokenLifeCycle, required refreshTokenLifeCycle})
      : _tokenLifeCycle = tokenLifeCycle,
        _refreshTokenLifeCycle = refreshTokenLifeCycle;

  factory JwtTokenEntity.from(JwtToken jwtToken,
      {required tokenMaxLifeTime, required refreshTokenMaxLifetime}) {
    final jwtTokenEntity = JwtTokenEntity(
        tokenLifeCycle: tokenMaxLifeTime,
        refreshTokenLifeCycle: refreshTokenMaxLifetime);
    jwtTokenEntity.accessToken = jwtToken.accessToken;
    jwtTokenEntity.refreshToken = jwtToken.refreshToken;
    final utcNow = DateTime.timestamp();
    jwtTokenEntity.accessTokenExpiry =
        utcNow.add(Duration(seconds: jwtToken.expiresIn));
    jwtTokenEntity.refreshTokenExpiry =
        utcNow.add(Duration(seconds: jwtToken.refreshExpiresIn));

    return jwtTokenEntity;
  }

  bool isAccessTokenExpired() {
    return _compareDateToNow(accessTokenExpiry) <= 0;
  }

  bool isRefreshTokenExpired() {
    return _compareDateToNow(refreshTokenExpiry) <= 0;
  }

  bool isTokenNearExpiry() {
    return _isBeyondThreshold(accessTokenExpiry, _tokenLifeCycle);
  }

  bool isRefreshTokenNearExpiry() {
    return _isBeyondThreshold(refreshTokenExpiry, _refreshTokenLifeCycle);
  }

  int _compareDateToNow(DateTime aDateTime) {
    final utcNow = DateTime.timestamp();
    return aDateTime.compareTo(utcNow);
  }

  bool _isBeyondThreshold(DateTime aDateTime, Duration lifetime) {
    DateTime now = DateTime.timestamp();
    Duration elapsedTime = aDateTime.difference(now);
    return elapsedTime <= lifetime;
  }

  Map<String, dynamic> toMap() {
    return {
      //Token unique name
      'token_name': TOKEN_NAME,
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'created_at': tokenCreationDate.millisecondsSinceEpoch,
      'token_expired_at': accessTokenExpiry.millisecondsSinceEpoch,
      'refresh_expired_at': refreshTokenExpiry.millisecondsSinceEpoch,
    };
  }

  @override
  String toString() {
    return 'JwtTokenEntity{accessToken: $accessToken, refreshToken: $refreshToken, '
        'accessTokenExpiry: $accessTokenExpiry, refreshTokenExpiry: $refreshTokenExpiry, '
        'tokenCreationDate: $tokenCreationDate}';
  }
}
