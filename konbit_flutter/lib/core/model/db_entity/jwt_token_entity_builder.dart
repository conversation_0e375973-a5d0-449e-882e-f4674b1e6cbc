import 'package:konbit_flutter/core/model/db_entity/jwt_token_entity.dart';
import 'package:konbit_flutter/core/model/output/jwt_token.dart';

import '../../config/konbit_config.dart';

class JwtTokenEntityBuilder {
  JwtTokenEntityBuilder._(JwtToken jwtToken) : _jwtToken = jwtToken;
  factory JwtTokenEntityBuilder.create(JwtToken jwtToken) {
    return JwtTokenEntityBuilder._(jwtToken);
  }

  final JwtToken _jwtToken;
  late Duration _tokenLifeTime = tokenThreshold;
  late Duration _refreshTokenLifeTime = refreshTokenThreshold;

  JwtTokenEntityBuilder tokenLifeThreshold(Duration threshold) {
    _tokenLifeTime = threshold;
    return this;
  }

  JwtTokenEntityBuilder refreshTokenLifeThreshold(Duration threshold) {
    _refreshTokenLifeTime = threshold;
    return this;
  }

  JwtTokenEntity build() {
    return JwtTokenEntity.from(_jwtToken,
        tokenMaxLifeTime: _tokenLifeTime,
        refreshTokenMaxLifetime: _refreshTokenLifeTime);
  }
}
