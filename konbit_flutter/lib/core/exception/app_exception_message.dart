import 'package:konbit_flutter/core/exception/app_exception.dart';

String exceptionKindToString(ExceptionKind kind, String defaultErr) {
  switch (kind) {
    case ExceptionKind.noInternet || ExceptionKind.networkTimeout:
      return "Failure to reach Konbit service. Check your internet connection or retry later";
    case ExceptionKind.konbitHttpError:
      return "Konbit Service seems unavailable at this moment. Please retry later on";
    default:
      return defaultErr;
  }
}
