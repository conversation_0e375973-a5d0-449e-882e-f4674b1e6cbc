import 'dart:async';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:konbit_flutter/core/model/output/http_konbit_message.dart';
import 'package:konbit_flutter/core/model/output/konbit_validation_error.dart';

import '../logger/logger.dart';
import 'app_exception_message.dart';

enum ExceptionKind {
  noInternet,
  networkTimeout,
  unknownFailure,
  badCertificate,
  konbitHttpError
}

class AppException implements Exception {
  final Exception exception;
  late ExceptionKind kind;
  String? message;
  HttpKonbitMessage? httpMessage;
  KonbitValidationError? konbitValidationError;

  AppException({required this.exception})
      : kind = _convertToExceptionKind(exception),
        httpMessage = _getKonbitHttpMessage(exception),
        konbitValidationError = _getKonbitValidationError(exception);

  String getHumanReadableError(String defaultError) {
    if (httpMessage != null) {
      logger.debug(httpMessage!.toJson().toString());
      switch (httpMessage!.status) {
        case KonbitMessageType.InvalidCredentials:
          return "Invalid username/password";
        case KonbitMessageType.UserDisabled:
          return "User has been disabled";
        case KonbitMessageType.EmailNotVerified:
          return "Account has not been activated. Please Verify your email";
        case KonbitMessageType.InvalidJwtToken:
          return "Invalid User Session. Please login again";
        case KonbitMessageType.NotAuthorized:
          return "Action is Forbidden. It may be due to invalid user session. Please login again";
        case KonbitMessageType.InternalError:
          return "Internal Server Error. Please retry later";
        default:
          return defaultError;
      }
    }

    if (konbitValidationError != null) {
      final List<String> errorList = List.of([], growable: true);
      for (var errorEntry in konbitValidationError!.errorMap.entries) {
        final fieldName = errorEntry.key;
        for (final fieldError in errorEntry.value) {
          errorList.add(
              "* $fieldName: ${fieldError.errorCode} - ${fieldError.errorDetail ?? ''}");
        }
      }

      return errorList.join("\n\n");
    }
    return exceptionKindToString(kind, defaultError);
  }

  @override
  String toString() {
    return getHumanReadableError(exception.toString());
  }
}

ExceptionKind _convertToExceptionKind(Exception ex) {
  if (ex is SocketException) {
    return ExceptionKind.noInternet;
  }

  if (ex is TimeoutException) {
    return ExceptionKind.networkTimeout;
  }

  if (ex is DioException) {
    switch (ex.type) {
      case DioExceptionType.sendTimeout ||
            DioExceptionType.receiveTimeout ||
            DioExceptionType.connectionTimeout:
        return ExceptionKind.networkTimeout;
      case DioExceptionType.badCertificate:
        return ExceptionKind.badCertificate;
      case DioExceptionType.connectionError:
        return ExceptionKind.noInternet;
      case DioExceptionType.badResponse:
        return ExceptionKind.konbitHttpError;
      default:
        return ExceptionKind.unknownFailure;
    }
  }

  return ExceptionKind.unknownFailure;
}

bool _isExceptionValid(Exception ex) {
  switch (_convertToExceptionKind(ex)) {
    case ExceptionKind.konbitHttpError:
      bool isExceptionValid = ex is DioException &&
          ex.response != null &&
          ex.response!.data != null;
      if (!isExceptionValid) {
        return false;
      }

      return true;
    default:
      return false;
  }
}

bool _isJsonValid(Exception ex) {
  // if this is not a Map. Possibly, it's not a serialized Json payload
  return ex is DioException && ex.response!.data is Map<String, dynamic>;
}

HttpKonbitMessage? _getKonbitHttpMessage(Exception ex) {
  if (!_isExceptionValid(ex) || !_isJsonValid(ex)) {
    return null;
  }

  final dioEx = ex as DioException;
  HttpKonbitMessage? konbitMessage;
  try {
    konbitMessage = HttpKonbitMessage.fromJson(
        dioEx.response!.data as Map<String, dynamic>);
  } catch (exception) {
    logger.warning(
        'Failure to serialize Error message for KonbitHttpMessage : ${exception.toString()}');
  } finally {
    logger.debug('KonbitMessage : ${konbitMessage?.toJson()}');
  }

  return konbitMessage;
}

KonbitValidationError? _getKonbitValidationError(Exception ex) {
  if (!_isExceptionValid(ex) || !_isJsonValid(ex)) {
    return null;
  }

  final dioEx = ex as DioException;
  if (dioEx.response!.statusCode != 400) {
    return null;
  }

  KonbitValidationError? konbitValidationError;
  try {
    konbitValidationError = KonbitValidationError.fromJson(
        dioEx.response!.data as Map<String, dynamic>);
  } catch (exception) {
    logger.warning(
        'Failure to serialize Error message for KonbitValidationError : ${exception.toString()}');
  } finally {
    logger
        .debug('KonbitValidationError : ${konbitValidationError?.toString()}');
  }

  return konbitValidationError;
}
