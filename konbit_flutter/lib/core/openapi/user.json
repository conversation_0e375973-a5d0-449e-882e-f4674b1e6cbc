{"openapi": "3.1.0", "info": {"title": "User Api", "description": "Api that manages user credentials", "license": {"name": "Konbit", "identifier": "PrivateKonbit"}, "version": "0.2.0"}, "paths": {"/konbit/api/user": {"post": {"tags": ["User Management"], "operationId": "register_user", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserSubscription"}}}, "required": true}, "responses": {"200": {"description": "On success return User Summary Info", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDtoInfo"}}}}, "400": {"description": "On invalid User Input will return InvalidContent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KonbitValidationError"}, "example": {"username": [{"error_code": "invalid_characters", "error_detail": "username has invalid characters.", "params": {}}], "email": [{"error_code": "invalid_email", "error_detail": "email is invalid", "params": {}}]}}}}, "403": {"description": "On Forbidden action will return NotAuthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "NotAuthorized", "status_code": 403, "message": "Action is Forbidden"}}}}, "500": {"description": "On Internal Server error such as MongoDB error. Should return InternalError", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error. <PERSON><PERSON> Later!"}}}}}}}, "/konbit/api/user/enable/{username}/{token}": {"get": {"tags": ["User Management"], "operationId": "enable_user", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}, {"name": "username", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "token", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"202": {"description": "On success return Action complete", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "ActionComplete", "status_code": 202, "message": "user account 'dessalines' has been activated"}}}}, "400": {"description": "On invalid User Input will return InvalidContent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KonbitValidationError"}, "example": {"activation_token": [{"error_code": "invalid_activation_token", "error_detail": "activation token cannot be empty", "params": {}}], "username": [{"error_code": "blank", "error_detail": "username cannot be empty", "params": {}}]}}}}, "403": {"description": "On Forbidden action will return NotAuthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "NotAuthorized", "status_code": 403, "message": "Action is Forbidden"}}}}, "404": {"description": "On NotFound http error should return NotFound message type", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "NotFound", "status_code": 404, "message": "Userna<PERSON> 'dumarsais' is invalid"}}}}, "500": {"description": "On Internal Server error such as MongoDB error. Should return InternalError", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error. <PERSON><PERSON> Later!"}}}}}}}, "/konbit/api/user/login": {"post": {"tags": ["User Management"], "operationId": "login", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginInfo"}}}, "required": true}, "responses": {"200": {"description": "Upon User Creation/Registration success return Action complete", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JwtToken"}, "example": {"access_token": "AAAAAA", "expires_in": 300, "refresh_expires_in": 1800, "refresh_token": "RRRR", "token_type": "Bearer", "not-before-policy": 0, "session_state": "3b237e96-3a07-48f4-8526-0dcca9a8a47e", "scope": "email profile"}}}}, "401": {"description": "On Forbidden action will return NotAuthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InvalidCredentials", "status_code": 401, "message": "Invalid User Credentials"}}}}, "403": {"description": "On Forbidden action will return NotAuthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "EmailNotVerified", "status_code": 403, "message": "User email not verified"}}}}, "500": {"description": "On Internal Server error such as MongoDB error. Should return InternalError", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error. <PERSON><PERSON> Later!"}}}}}}}, "/konbit/api/user/logout": {"post": {"tags": ["User Management"], "operationId": "logout", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshToken"}}}, "required": true}, "responses": {"200": {"description": "On logout Success", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "ActionComplete", "status_code": 200, "message": "User session Logged out"}}}}, "403": {"description": "Logout Failure will respond with Forbidden if rejected", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InvalidCredentials", "status_code": 403, "message": "Authentication Server rejects session logout"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error"}}}}}}}, "/konbit/api/user/refresh_token": {"post": {"tags": ["User Management"], "operationId": "refresh_token", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/RefreshToken"}}}, "required": true}, "responses": {"200": {"description": "Upon Refresh Token success return Action complete", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JwtToken"}, "example": {"access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c", "expires_in": 300, "refresh_expires_in": 1800, "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6InRvdXNzYWludCIsImlhdCI6MTUxNjIzOTAyMn0.eUv7v_WIEU4TJdcBV6gARiiAer_u3ukFexC1YN8LaYA", "token_type": "Bearer", "not-before-policy": 0, "session_state": "3b237e96-3a07-48f4-8526-0dcca9a8a47e", "scope": "email profile"}}}}, "401": {"description": "On invalid Refresh <PERSON>(expired or malformed token)", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InvalidJwtToken", "status_code": 401, "message": "Invalid Refresh <PERSON>"}}}}, "500": {"description": "Internal Server Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error"}}}}}}}}, "components": {"schemas": {"FieldError": {"type": "object", "required": ["error_code", "params"], "properties": {"error_code": {"type": "string"}, "error_detail": {"type": ["string", "null"]}, "params": {"type": "object", "additionalProperties": {}, "propertyNames": {"type": "string"}}}}, "HttpKonbitMessage": {"type": "object", "required": ["status", "status_code", "message"], "properties": {"message": {"type": "string"}, "status": {"$ref": "#/components/schemas/KonbitMessageType"}, "status_code": {"type": "integer", "format": "int32", "minimum": 0}}}, "JwtToken": {"type": "object", "required": ["access_token", "expires_in", "refresh_expires_in", "refresh_token", "token_type", "not-before-policy", "session_state", "scope"], "properties": {"access_token": {"type": "string"}, "expires_in": {"type": "integer", "format": "int64"}, "not-before-policy": {"type": "integer", "format": "int32"}, "refresh_expires_in": {"type": "integer", "format": "int64"}, "refresh_token": {"type": "string"}, "scope": {"type": "string"}, "session_state": {"type": "string"}, "token_type": {"type": "string"}}}, "KonbitMessageType": {"type": "string", "enum": ["ActionComplete", "EmailNotVerified", "UserDisabled", "InvalidCredentials", "InvalidJwtToken", "DuplicateProfile", "UsernameAlreadyExist", "EmailAlreadyExist", "InternalError", "NotAuthorized", "NotFound", "Invalid<PERSON>ontent", "UnexpectedFailure", "GeocodingLookupError"]}, "KonbitValidationError": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/FieldError"}}, "propertyNames": {"type": "string"}}, "LoginInfo": {"type": "object", "required": ["username", "password"], "properties": {"password": {"type": "string"}, "username": {"type": "string"}}}, "RefreshToken": {"type": "object", "required": ["token"], "properties": {"token": {"type": "string"}}}, "UserDtoInfo": {"type": "object", "required": ["username", "email", "email_verified", "enabled", "role", "created_at", "updated_at", "last_seen"], "properties": {"created_at": {"type": "string", "format": "date-time"}, "email": {"type": "string"}, "email_verified": {"type": "boolean"}, "enabled": {"type": "boolean"}, "last_seen": {"type": "string", "format": "date-time"}, "role": {"$ref": "#/components/schemas/UserRole"}, "updated_at": {"type": "string", "format": "date-time"}, "username": {"type": "string"}}}, "UserRole": {"type": "string", "enum": ["regular", "admin"]}, "UserSubscription": {"type": "object", "required": ["username", "email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string"}, "username": {"type": "string"}}}}}}