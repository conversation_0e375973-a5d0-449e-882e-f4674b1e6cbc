// Openapi Generator last run: : 2025-05-20T16:40:38.941400
import 'package:openapi_generator_annotations/openapi_generator_annotations.dart';

@Openapi(
  additionalProperties:
      AdditionalProperties(pubName: 'user_api', pubAuthor: '<PERSON>'),
  inputSpec: InputSpec(path: 'lib/core/openapi/user.json'),
  generatorName: Generator.dio,
  outputDirectory: './api/user_api', //this will be at the root of the project
)
class UserConfig {}

//import 'package:openapi_generator_annotations/openapi_generator_annotations.dart';

@Openapi(
  additionalProperties:
      AdditionalProperties(pubName: 'user_profile_api', pubAuthor: 'Doug'),
  inputSpec: InputSpec(path: 'lib/core/openapi/user_profile.json'),
  generatorName: Generator.dio,
  outputDirectory:
      './api/user_profile_api', //this will be at the root of the project
)
class UserProfileConfig {}