{"openapi": "3.1.0", "info": {"title": "User Profile Api", "description": "Api that manages user profile metadata such as user profession and location", "license": {"name": "Konbit", "identifier": "PrivateKonbit"}, "version": "0.2.0"}, "paths": {"/konbit/api/profile": {"get": {"tags": ["User Profile Management"], "operationId": "get_my_profile", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}], "responses": {"200": {"description": "Show User private Profile info successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileDto"}, "example": {"username": "", "first_name": null, "middle_name": null, "last_name": null, "birth_date": null, "birth_month": null, "birth_year": null, "nationality": null, "education": null, "phone": null, "main_occupation": "", "secondary_occupation": null, "skills": [], "street_address": "", "address_line_2": null, "city": "", "locality_1": "", "locality_2": null, "zip_code": "", "country_code": "", "created_at": "1970-01-01T00:00:00Z", "updated_at": "1970-01-01T00:00:00Z", "visible": false, "profile_pic": null, "bio": null, "longitude": 0, "latitude": 0}}}}, "401": {"description": "On Invalid Jwt token will return NotAuthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "NotAuthorized", "status_code": 401, "message": "Invalid User Credentials"}}}}, "403": {"description": "On Forbidden action will return one of the error from KonbitMessageType enum", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "DuplicateProfile", "status_code": 403, "message": "User profile already exists"}}}}, "404": {"description": "On Not Found error Should return NotFound message type", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "NotFound", "status_code": 404, "message": "Profile Not Found"}}}}, "406": {"description": "On Invalid Input for userprofile operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "Invalid<PERSON>ontent", "status_code": 406, "message": "Invalid Grant or Credentials"}}}}, "500": {"description": "On Internal Server error such as MongoDB error. Should return InternalError", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error. <PERSON><PERSON> Later!"}}}}}, "security": [{"authBearer": []}]}, "post": {"tags": ["User Profile Management"], "operationId": "create_profile", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileForm"}}}, "required": true}, "responses": {"201": {"description": "Upon User Creation/Registration success return Action complete", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "ActionComplete", "status_code": 201, "message": "Profile created Successfully"}}}}, "400": {"description": "On invalid User Input will return InvalidContent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KonbitValidationError"}, "example": {"birthday": [{"error_code": "invalid_birthday", "error_detail": "Birthday date is out of range", "params": {}}], "country": [{"error_code": "invalid_length", "error_detail": "Country length must be exactly 3 characters as country iso3 code", "params": {}}]}}}}, "401": {"description": "On Forbidden action will return NotAuthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "NotAuthorized", "status_code": 401, "message": "Invalid User Credentials"}}}}, "403": {"description": "On Forbidden action will return one of the error from KonbitMessageType enum", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "DuplicateProfile", "status_code": 403, "message": "User profile already exists"}}}}, "404": {"description": "On Not Found error Should return NotFound message type", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "NotFound", "status_code": 404, "message": "User Not Found"}}}}, "406": {"description": "On Invalid Input for userprofile operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "Invalid<PERSON>ontent", "status_code": 406, "message": "Invalid Grant or Credentials"}}}}, "500": {"description": "On Internal Server error such as MongoDB error. Should return InternalError", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "example": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error. <PERSON><PERSON> Later!"}}}}}, "security": [{"authBearer": []}]}}, "/konbit/api/profile/location": {"get": {"tags": ["User Profile Management"], "operationId": "get_profiles_by_location", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}, {"name": "occupation", "in": "query", "required": false, "schema": {"type": ["string", "null"]}}, {"name": "latitude", "in": "query", "required": true, "schema": {"type": "number", "format": "double"}, "example": 45.47669563462626}, {"name": "longitude", "in": "query", "required": true, "schema": {"type": "number", "format": "double"}, "example": -75.68910513108277}, {"name": "max_distance", "in": "query", "required": false, "schema": {"type": ["integer", "null"], "format": "int32"}, "example": 10000}, {"name": "direction", "in": "query", "required": false, "schema": {"oneOf": [{"type": "null"}, {"$ref": "#/components/schemas/CursorDirection"}]}}, {"name": "cursor", "in": "query", "required": false, "schema": {"type": ["string", "null"]}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": ["integer", "null"], "format": "int64"}, "example": 20}], "responses": {"200": {"description": "Return an array of User Profiles close to a gps coordinates location at a maximum distance", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthLocationQueryResult"}, "examples": {"Public Profile Information with Authentication": {"value": {"total_items": 1, "prev": null, "next": null, "profiles": [{"username": "", "education": null, "main_occupation": "", "other_occupation": null, "skills": [], "city": "", "locality_1": null, "zip_code": null, "country_code": null, "distance": null, "row_number": null, "pic_url": null, "bio": null, "longitude": 0, "latitude": 0}]}}, "Public Profile Information without Authentication": {"value": {"total_items": 1, "prev": null, "next": null, "profiles": [{"username": "", "main_occupation": "", "other_occupation": null, "skills": [], "city": "", "country_code": null, "distance": null, "row_number": null}]}}}}}}, "400": {"description": "On invalid User Input will return InvalidContent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Email taken": {"value": {"status": "Invalid<PERSON>ontent", "status_code": 400, "message": "Bad username or Token"}}}}}}, "401": {"description": "On Forbidden action will return NotAuthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Forbidden Action": {"value": {"status": "NotAuthorized", "status_code": 401, "message": "Invalid User Credentials"}}}}}}, "403": {"description": "On Forbidden action will return one of the error from KonbitMessageType enum", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Forbidden Action": {"value": {"status": "DuplicateProfile", "status_code": 403, "message": "User profile already exists"}}}}}}, "404": {"description": "On Not Found error Should return NotFound message type", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Server Error": {"value": {"status": "InternalError", "status_code": 404, "message": "Profile Not Found"}}}}}}, "406": {"description": "On Invalid Input for userprofile operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Server Error": {"value": {"status": "Invalid<PERSON>ontent", "status_code": 406, "message": "Invalid Grant or Credentials"}}}}}}, "500": {"description": "On Internal Server error such as MongoDB error. Should return InternalError", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Server Error": {"value": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error. <PERSON><PERSON> Later!"}}}}}}}, "security": [{}, {"authBearer": []}]}}, "/konbit/api/profile/public/{username}": {"get": {"tags": ["User Profile Management"], "operationId": "get_a_profile", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}, {"name": "username", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Show a public User Profile details either with or without authentication", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserProfileSummary"}, "examples": {"Public Profile Information with Authentication": {"value": {"username": "", "education": null, "main_occupation": "", "other_occupation": null, "skills": [], "city": "", "locality_1": null, "zip_code": null, "country_code": null, "distance": null, "row_number": null, "pic_url": null, "bio": null, "longitude": 0, "latitude": 0}}, "Public Profile Information without Authentication": {"value": {"username": "", "main_occupation": "", "other_occupation": null, "skills": [], "city": "", "country_code": null, "distance": null, "row_number": null}}}}}}, "400": {"description": "On invalid User Input will return InvalidContent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Email taken": {"value": {"status": "Invalid<PERSON>ontent", "status_code": 400, "message": "Bad username or Token"}}}}}}, "401": {"description": "On Forbidden action will return NotAuthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Forbidden Action": {"value": {"status": "NotAuthorized", "status_code": 401, "message": "Invalid User Credentials"}}}}}}, "403": {"description": "On Forbidden action will return one of the error from KonbitMessageType enum", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Forbidden Action": {"value": {"status": "DuplicateProfile", "status_code": 403, "message": "User profile already exists"}}}}}}, "404": {"description": "On Not Found error Should return NotFound message type", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Server Error": {"value": {"status": "InternalError", "status_code": 404, "message": "Profile Not Found"}}}}}}, "406": {"description": "On Invalid Input for userprofile operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Server Error": {"value": {"status": "Invalid<PERSON>ontent", "status_code": 406, "message": "Invalid Grant or Credentials"}}}}}}, "500": {"description": "On Internal Server error such as MongoDB error. Should return InternalError", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Server Error": {"value": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error. <PERSON><PERSON> Later!"}}}}}}}, "security": [{}, {"authBearer": []}]}}, "/konbit/api/profile/region": {"get": {"tags": ["User Profile Management"], "operationId": "get_profiles_by_region", "parameters": [{"name": "x-client-trace-id", "in": "header", "description": "An optional unique randomly generated value to track the request for event correlation", "required": false, "schema": {"type": ["string", "null"]}, "example": "1643b1fa-0370-4670-ba4a-ab65de029775"}, {"name": "occupation", "in": "query", "required": false, "schema": {"type": ["string", "null"]}}, {"name": "city", "in": "query", "required": true, "schema": {"type": "string"}, "example": "Port-au-Prince"}, {"name": "country", "in": "query", "required": true, "schema": {"type": "string", "maxLength": 3, "minLength": 3}, "example": "HAI"}, {"name": "direction", "in": "query", "required": false, "schema": {"oneOf": [{"type": "null"}, {"$ref": "#/components/schemas/CursorDirection"}]}}, {"name": "cursor", "in": "query", "required": false, "schema": {"type": ["string", "null"]}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": ["integer", "null"], "format": "int64"}, "example": 20}], "responses": {"200": {"description": "Return an array of User Profiles found in the specified country and city", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthLocationQueryResult"}, "examples": {"Public Profile Information with Authentication": {"value": {"total_items": 1, "prev": null, "next": null, "profiles": [{"username": "", "education": null, "main_occupation": "", "other_occupation": null, "skills": [], "city": "", "locality_1": null, "zip_code": null, "country_code": null, "distance": null, "row_number": null, "pic_url": null, "bio": null, "longitude": 0, "latitude": 0}]}}, "Public Profile Information without Authentication": {"value": {"total_items": 1, "prev": null, "next": null, "profiles": [{"username": "<PERSON><PERSON><PERSON><PERSON>", "main_occupation": "", "other_occupation": null, "skills": [], "city": "", "country_code": null, "distance": null, "row_number": null}]}}}}}}, "400": {"description": "On invalid User Input will return InvalidContent", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Email taken": {"value": {"status": "Invalid<PERSON>ontent", "status_code": 400, "message": "Bad username or Token"}}}}}}, "401": {"description": "On Forbidden action will return NotAuthorized", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Forbidden Action": {"value": {"status": "NotAuthorized", "status_code": 401, "message": "Invalid User Credentials"}}}}}}, "403": {"description": "On Forbidden action will return one of the error from KonbitMessageType enum", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Forbidden Action": {"value": {"status": "DuplicateProfile", "status_code": 403, "message": "User profile already exists"}}}}}}, "404": {"description": "On Not Found error Should return NotFound message type", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Server Error": {"value": {"status": "InternalError", "status_code": 404, "message": "Profile Not Found"}}}}}}, "406": {"description": "On Invalid Input for userprofile operation", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Server Error": {"value": {"status": "Invalid<PERSON>ontent", "status_code": 406, "message": "Invalid Grant or Credentials"}}}}}}, "500": {"description": "On Internal Server error such as MongoDB error. Should return InternalError", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HttpKonbitMessage"}, "examples": {"Server Error": {"value": {"status": "InternalError", "status_code": 500, "message": "Internal Server Error. <PERSON><PERSON> Later!"}}}}}}}, "security": [{}, {"authBearer": []}]}}}, "components": {"schemas": {"AuthLocationQueryResult": {"type": "object", "required": ["total_items", "profiles"], "properties": {"next": {"type": ["string", "null"]}, "prev": {"type": ["string", "null"]}, "profiles": {"type": "array", "items": {"$ref": "#/components/schemas/UserProfileSummary"}}, "total_items": {"type": "integer", "format": "int64", "minimum": 0}}}, "CursorDirection": {"type": "string", "enum": ["next", "prev"]}, "FieldError": {"type": "object", "required": ["error_code", "params"], "properties": {"error_code": {"type": "string"}, "error_detail": {"type": ["string", "null"]}, "params": {"type": "object", "additionalProperties": {}, "propertyNames": {"type": "string"}}}}, "HttpKonbitMessage": {"type": "object", "required": ["status", "status_code", "message"], "properties": {"message": {"type": "string"}, "status": {"$ref": "#/components/schemas/KonbitMessageType"}, "status_code": {"type": "integer", "format": "int32", "minimum": 0}}}, "JwtToken": {"type": "object", "required": ["access_token", "expires_in", "refresh_expires_in", "refresh_token", "token_type", "not-before-policy", "session_state", "scope"], "properties": {"access_token": {"type": "string"}, "expires_in": {"type": "integer", "format": "int64"}, "not-before-policy": {"type": "integer", "format": "int32"}, "refresh_expires_in": {"type": "integer", "format": "int64"}, "refresh_token": {"type": "string"}, "scope": {"type": "string"}, "session_state": {"type": "string"}, "token_type": {"type": "string"}}}, "KonbitMessageType": {"type": "string", "enum": ["ActionComplete", "EmailNotVerified", "UserDisabled", "InvalidCredentials", "InvalidJwtToken", "DuplicateProfile", "UsernameAlreadyExist", "EmailAlreadyExist", "InternalError", "NotAuthorized", "NotFound", "Invalid<PERSON>ontent", "UnexpectedFailure", "GeocodingLookupError"]}, "KonbitValidationError": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/FieldError"}}, "propertyNames": {"type": "string"}}, "LocationQuery": {"type": "object", "required": ["latitude", "longitude"], "properties": {"cursor": {"type": ["string", "null"]}, "direction": {"oneOf": [{"type": "null"}, {"$ref": "#/components/schemas/CursorDirection"}]}, "latitude": {"type": "number", "format": "double"}, "limit": {"type": ["integer", "null"], "format": "int64"}, "longitude": {"type": "number", "format": "double"}, "max_distance": {"type": ["integer", "null"], "format": "int32"}, "occupation": {"type": ["string", "null"]}}}, "LocationQueryResult": {"type": "object", "required": ["total_items", "profiles"], "properties": {"next": {"type": ["string", "null"]}, "prev": {"type": ["string", "null"]}, "profiles": {"type": "array", "items": {"$ref": "#/components/schemas/UserPublicProfile"}}, "total_items": {"type": "integer", "format": "int64", "minimum": 0}}}, "RegionQuery": {"type": "object", "required": ["city", "country"], "properties": {"city": {"type": "string"}, "country": {"type": "string"}, "cursor": {"type": ["string", "null"]}, "direction": {"oneOf": [{"type": "null"}, {"$ref": "#/components/schemas/CursorDirection"}]}, "limit": {"type": ["integer", "null"], "format": "int64"}, "occupation": {"type": ["string", "null"]}}}, "UserProfileDto": {"type": "object", "required": ["username", "main_occupation", "skills", "street_address", "city", "locality_1", "zip_code", "country_code", "created_at", "updated_at", "visible", "longitude", "latitude"], "properties": {"address_line_2": {"type": ["string", "null"]}, "bio": {"type": ["string", "null"]}, "birth_date": {"type": ["integer", "null"], "format": "int32", "minimum": 0}, "birth_month": {"type": ["integer", "null"], "format": "int32", "minimum": 0}, "birth_year": {"type": ["integer", "null"], "format": "int32", "minimum": 0}, "city": {"type": "string"}, "country_code": {"type": "string"}, "created_at": {"type": "string", "format": "date-time"}, "education": {"type": ["string", "null"]}, "first_name": {"type": ["string", "null"]}, "last_name": {"type": ["string", "null"]}, "latitude": {"type": "number", "format": "double"}, "locality_1": {"type": "string"}, "locality_2": {"type": ["string", "null"]}, "longitude": {"type": "number", "format": "double"}, "main_occupation": {"type": "string"}, "middle_name": {"type": ["string", "null"]}, "nationality": {"type": ["string", "null"]}, "phone": {"type": ["string", "null"]}, "profile_pic": {"type": ["string", "null"]}, "secondary_occupation": {"type": ["string", "null"]}, "skills": {"type": "array", "items": {"type": "string"}}, "street_address": {"type": "string"}, "updated_at": {"type": "string", "format": "date-time"}, "username": {"type": "string"}, "visible": {"type": "boolean"}, "zip_code": {"type": "string"}}}, "UserProfileForm": {"type": "object", "required": ["main_occupation", "skills", "country"], "properties": {"birth_date": {"type": ["integer", "null"], "format": "int32", "example": 30, "minimum": 0}, "birth_month": {"type": ["integer", "null"], "format": "int32", "example": 8, "minimum": 0}, "birth_year": {"type": ["integer", "null"], "format": "int32", "example": 1988, "minimum": 0}, "city": {"type": ["string", "null"], "example": "Port-au-Prince"}, "country": {"type": "string"}, "education": {"type": ["string", "null"]}, "first_name": {"type": ["string", "null"]}, "last_name": {"type": ["string", "null"]}, "latitude": {"type": ["number", "null"], "format": "double", "example": 18.704617364983488}, "locality_1": {"type": ["string", "null"], "example": "Ouest"}, "locality_2": {"type": ["string", "null"]}, "longitude": {"type": ["number", "null"], "format": "double", "example": -72.14350548037115}, "main_occupation": {"type": "string"}, "middle_name": {"type": ["string", "null"]}, "nationality": {"type": ["string", "null"]}, "phone": {"type": ["string", "null"]}, "second_occupation": {"type": ["string", "null"]}, "skills": {"type": "array", "items": {"type": "string"}}, "street_address": {"type": ["string", "null"]}, "zip_code": {"type": ["string", "null"]}}}, "UserProfileSummary": {"type": "object", "required": ["username", "main_occupation", "skills", "city", "longitude", "latitude"], "properties": {"bio": {"type": ["string", "null"]}, "city": {"type": "string"}, "country_code": {"type": ["string", "null"]}, "distance": {"type": ["number", "null"], "format": "double"}, "education": {"type": ["string", "null"]}, "latitude": {"type": "number", "format": "double"}, "locality_1": {"type": ["string", "null"]}, "longitude": {"type": "number", "format": "double"}, "main_occupation": {"type": "string"}, "other_occupation": {"type": ["string", "null"]}, "pic_url": {"type": ["string", "null"]}, "row_number": {"type": ["integer", "null"], "format": "int64", "minimum": 0}, "skills": {"type": "array", "items": {"type": "string"}}, "username": {"type": "string"}, "zip_code": {"type": ["string", "null"]}}}, "UserPublicProfile": {"type": "object", "required": ["username", "main_occupation", "skills", "city"], "properties": {"city": {"type": "string"}, "country_code": {"type": ["string", "null"]}, "distance": {"type": ["number", "null"], "format": "double"}, "main_occupation": {"type": "string"}, "other_occupation": {"type": ["string", "null"]}, "row_number": {"type": ["integer", "null"], "format": "int64", "minimum": 0}, "skills": {"type": "array", "items": {"type": "string"}}, "username": {"type": "string"}}}}, "securitySchemes": {"authBearer": {"type": "http", "scheme": "bearer"}}}}