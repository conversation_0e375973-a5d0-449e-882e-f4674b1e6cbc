import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class KonbitCacheManager {
  static const key = 'konbitCacheKey';
  static CacheManager instance = CacheManager(
    Config(
      key,
      stalePeriod: const Duration(seconds: 60),
      maxNrOfCacheObjects: 20,
      repo: JsonCacheInfoRepository(databaseName: key),
      fileSystem: IOFileSystem(key),
      fileService: HttpFileService(),
    ),
  );
}
