const Duration tokenThreshold = Duration(minutes: 3, seconds: 30);
const Duration refreshTokenThreshold = Duration(minutes: 5, seconds: 30);
const String GEO_DB_FILENAME = 'world_data.db';

class KonbitConfig {
  String _httpScheme;
  String _server;
  int _port;

  static KonbitConfig? _defaultConfig;

  KonbitConfig._(
      {String httpScheme = 'http', String host = '*********', int port = 9080})
      : _httpScheme = httpScheme,
        _server = host,
        _port = port;

  factory KonbitConfig.getConfig() {
    _defaultConfig = _defaultConfig ??= KonbitConfig._();
    return _defaultConfig!;
  }

  int get port => _port;

  set port(int value) {
    _port = value;
  }

  String get server => _server;

  set server(String value) {
    _server = value;
  }

  String get httpScheme => _httpScheme;

  set httpScheme(String value) {
    _httpScheme = value;
  }

  String getUrl() {
    return '$_httpScheme://$_server:$_port';
  }

  @override
  String toString() {
    return getUrl();
  }
}
