import 'dart:io';

import 'package:dio/dio.dart';
import 'package:konbit_flutter/core/api/base_api.dart';
import 'package:konbit_flutter/core/exception/app_exception.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/core/model/output/http_konbit_message.dart';

class ProfilePictureApi extends BaseApi {
  ProfilePictureApi(super.dio, super.cancelToken);

  static const String baseUri = "/konbit/api/photo";

  Future<Response<HttpKonbitMessage>?> uploadProfilePicture(File file,
      {Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    Response<HttpKonbitMessage>? response;
    try {
      final formData = FormData.fromMap({
        'picture_file': await MultipartFile.fromFile(file.path,
            filename: file.path.split('/').last)
      });

      response = await dioWrapper.post(baseUri, formData,
          options: options,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);
    } on DioException catch (e) {
      logger.debug('Exception thrown : ${e.toString()}');
      throw AppException(exception: e);
    }

    return response;
  }
}
