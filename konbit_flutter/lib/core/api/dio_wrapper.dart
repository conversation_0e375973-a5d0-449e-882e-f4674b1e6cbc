import 'package:dio/dio.dart';
import 'package:konbit_flutter/core/api/response_builder.dart';
import 'package:konbit_flutter/core/model/output/auth_location_query_result.dart';

import '../model/output/http_konbit_message.dart';
import '../model/output/jwt_token.dart';
import '../model/output/user_dto_info.dart';
import '../model/output/user_profile_dto.dart';
import '../model/output/user_profile_search.dart';

class DioWrapper {
  DioWrapper({required Dio dio, CancelToken? cancelToken})
      : _dio = dio,
        _cancelToken = cancelToken;

  final Dio _dio;
  final CancelToken? _cancelToken;

  Future<Response<T>> post<T>(String uri, Object model,
      {Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    var response = await _dio.post(
      uri,
      data: model,
      cancelToken: _cancelToken,
      onSendProgress: onSendProgress,
      onReceiveProgress: onReceiveProgress,
      options: Options(contentType: Headers.jsonContentType),
    );

    return await convertResponse<T>(response);
  }

  Future<Response<T>> get<T>(String uri,
      {Map<String, dynamic>? queryParameters,
      Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    var response = await _dio.get(
      uri,
      queryParameters: queryParameters,
      cancelToken: _cancelToken,
      onReceiveProgress: onReceiveProgress,
      options: Options(contentType: Headers.jsonContentType),
    );

    return await convertResponse<T>(response);
  }

  Future<Response<T>> convertResponse<T>(Response inputResponse) async {
    Response<T>? response;
    if (T == JwtToken) {
      final jwtToken = JwtToken.fromJson(inputResponse.data!);
      response = buildResponse<T>(inputResponse, jwtToken as T);
    }

    if (T == HttpKonbitMessage) {
      final konbitMessage = HttpKonbitMessage.fromJson(inputResponse.data!);
      response = buildResponse<T>(inputResponse, konbitMessage as T);
    }

    if (T == UserDtoInfo) {
      final userDtoInfo = UserDtoInfo.fromJson(inputResponse.data!);
      response = buildResponse<T>(inputResponse, userDtoInfo as T);
    }

    if (T == UserProfileDto) {
      final profileDto = UserProfileDto.fromJson(inputResponse.data!);
      response = buildResponse<T>(inputResponse, profileDto as T);
    }

    if (T == UserProfileSummary) {
      final profileSummary = UserProfileSummary.fromJson(inputResponse.data!);
      response = buildResponse<T>(inputResponse, profileSummary as T);
    }

    if (T == List<UserProfileSummary>) {
      final profileSummaries = (inputResponse.data! as List)
          .map((data) => UserProfileSummary.fromJson(data))
          .toList();

      response = buildResponse<T>(inputResponse, profileSummaries as T);
      //debugPrint('A list of UserProfile Summary : $T');
    }

    if (T == AuthLocationQueryResult) {
      final authLocationResult =
          AuthLocationQueryResult.fromJson(inputResponse.data!);
      response = buildResponse<T>(inputResponse, authLocationResult as T);
      //debugPrint('A list of UserProfile Summary : $T');
    }

    if (response == null) {
      throw UnimplementedError('Conversion is not implemented for type $T');
    }

    return response;
  }
}
