import 'package:dio/dio.dart';
import 'package:konbit_flutter/core/api/base_api.dart';
import 'package:konbit_flutter/core/exception/app_exception.dart';
import 'package:konbit_flutter/core/model/input/login_info.dart';
import 'package:konbit_flutter/core/model/input/refresh_token.dart';
import 'package:konbit_flutter/core/model/input/user_subscription.dart';
import 'package:konbit_flutter/core/model/output/http_konbit_message.dart';
import 'package:konbit_flutter/core/model/output/jwt_token.dart';
import 'package:konbit_flutter/core/model/output/user_dto_info.dart';

import '../logger/logger.dart';

class UserApi extends BaseApi {
  UserApi(super.dio, super.cancelToken);

  static const String baseUri = "/konbit/api/user";

  Future<Response<UserDtoInfo>?> registerUser(UserSubscription subscription,
      {Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    Response<UserDtoInfo>? response;

    try {
      response = await dioWrapper.post<UserDtoInfo>(baseUri, subscription,
          options: options,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);
    } on DioException catch (e) {
      logger.debug('Exception thrown : ${e.toString()}');
      throw AppException(exception: e);
    }

    return response;
  }

  Future<Response<JwtToken>?> login(LoginInfo loginInfo,
      {Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    Response<JwtToken>? response;
    try {
      response = await dioWrapper.post<JwtToken>('$baseUri/login', loginInfo,
          options: options,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);
    } on DioException catch (dioEx) {
      throw AppException(exception: dioEx);
    }

    return response;
  }

  Future<Response<HttpKonbitMessage>?> logout(String token,
      {Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    Response<HttpKonbitMessage>? response;
    try {
      final refreshToken = RefreshToken(token: token);
      response = await dioWrapper.post<HttpKonbitMessage>(
          '$baseUri/logout', refreshToken,
          options: options,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);
    } on DioException catch (dioEx) {
      throw AppException(exception: dioEx);
    }

    return response;
  }

  Future<Response<JwtToken>?> refreshToken(String token,
      {Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    final refreshToken = RefreshToken(token: token);
    Response<JwtToken>? response;
    try {
      response = await dioWrapper.post<JwtToken>(
          '$baseUri/refresh_token', refreshToken,
          options: options,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);
    } on DioException catch (dioEx) {
      throw AppException(exception: dioEx);
    }

    return response;
  }
}

//Future<Response>
