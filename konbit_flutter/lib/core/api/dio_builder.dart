import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:konbit_flutter/core/api/retry_policy/retry_backoffs.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:talker_dio_logger/talker_dio_logger_interceptor.dart';
import 'package:talker_dio_logger/talker_dio_logger_settings.dart';

import 'interceptor/http_interceptor.dart';
import 'package:dio_smart_retry/dio_smart_retry.dart';

class DioBuilder {
  DioBuilder._(Dio dio) : _dio = dio;

  factory DioBuilder.create(
      {BaseOptions? baseOptions, bool retryEnabled = true, int? maxRetries}) {
    Dio dio = Dio(baseOptions);
    dio.interceptors.add(DefaultInterceptor.getSingleInstance());

    if (retryEnabled || maxRetries != null) {
      final expBackOffs = maxRetries != null
          ? exponentialBackOffs(maxRetries)
          : exponentialBackOffs();

      dio.interceptors.add(RetryInterceptor(
        dio: dio,
        logPrint: (message) => logger.debug("Retry Attempt --- $message"),
        retries: expBackOffs.length, // retry count (optional)
        retryDelays: expBackOffs,
      ));
    }

    if (kDebugMode) {
      dio.interceptors.add(
        TalkerDioLogger(
          settings: const TalkerDioLoggerSettings(
            printRequestHeaders: true,
            printResponseHeaders: true,
            printResponseMessage: true,
          ),
        ),
      );
    }

    return DioBuilder._(dio);
  }

  final Dio _dio;

  DioBuilder baseUrl(String rootUrl) {
    _dio.options.baseUrl = rootUrl;
    return this;
  }

  DioBuilder connectTimeout(Duration timeout) {
    _dio.options.connectTimeout = timeout;
    return this;
  }

  DioBuilder sendTimeout(Duration timeout) {
    _dio.options.sendTimeout = timeout;
    return this;
  }

  DioBuilder receiveTimeout(Duration timeout) {
    _dio.options.receiveTimeout = timeout;
    return this;
  }

  DioBuilder interceptor(Interceptor interceptor) {
    _dio.interceptors.add(interceptor);
    return this;
  }

  DioBuilder interceptors(List<Interceptor> interceptor) {
    _dio.interceptors.addAll(interceptor);
    return this;
  }

  Dio build() {
    return _dio;
  }
}
