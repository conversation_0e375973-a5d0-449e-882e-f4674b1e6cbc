import 'dart:math';

int _getRandomMilliSeconds() => Random().nextInt(50) + 300;

List<Duration> exponentialBackOffs([int maxRetries = 5]) {
  int retryDelay = 1;
  final List<Duration> backoffs = List.empty(growable: true);

  for (int i = 1; i <= maxRetries; i++) {
    backoffs.add(
        Duration(seconds: retryDelay, milliseconds: _getRandomMilliSeconds()));
    retryDelay = 1 + Random().nextInt(4);
  }
  return backoffs;
}
