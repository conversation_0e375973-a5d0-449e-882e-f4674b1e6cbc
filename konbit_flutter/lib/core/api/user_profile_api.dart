import 'package:dio/dio.dart';
import 'package:konbit_flutter/core/api/base_api.dart';
import 'package:konbit_flutter/core/model/input/query/location_query.dart';
import 'package:konbit_flutter/core/model/input/user_profile_form.dart';
import 'package:konbit_flutter/core/model/output/http_konbit_message.dart';
import 'package:konbit_flutter/core/model/output/user_profile_dto.dart';

import '../exception/app_exception.dart';
import '../logger/logger.dart';
import '../model/output/auth_location_query_result.dart';

class UserProfileApi extends BaseApi {
  UserProfileApi(super.dio, super.cancelToken);

  static const String baseUri = "/konbit/api/profile";

  /// Retrieve User Personal Profile information
  /// Must have an Authorization header set to a  "Bearer " token
  /// this can be achieved by adding an interceptor that will inject an authorization header
  /// dio.intercetors.add(AccessTokenInterceptor(jwtTokenString))
  Future<Response<UserProfileDto>?> getMyProfile(
      {Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    Response<UserProfileDto>? response;
    try {
      response = await dioWrapper.get<UserProfileDto>(baseUri,
          options: options,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);
    } on DioException catch (e) {
      logger.debug('Exception thrown : ${e.toString()}');
      throw AppException(exception: e);
    }

    return response;
  }

  Future<Response<HttpKonbitMessage>?> createProfile(
      UserProfileForm userProfileForm,
      {Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    Response<HttpKonbitMessage>? response;
    try {
      response = await dioWrapper.post(baseUri, userProfileForm,
          options: options,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);
    } on DioException catch (e) {
      logger.debug('Exception thrown : ${e.toString()}');
      throw AppException(exception: e);
    }

    return response;
  }

  Future<Response<AuthLocationQueryResult>?> getProfilesByLocation(
      LocationQuery locationQuery,
      {Options? options,
      Function(int, int)? onSendProgress,
      void Function(int, int)? onReceiveProgress}) async {
    Response<AuthLocationQueryResult>? response;
    try {
      response = await dioWrapper.get<AuthLocationQueryResult>(
          '$baseUri/location',
          queryParameters: locationQuery.toJson(),
          options: options,
          onSendProgress: onSendProgress,
          onReceiveProgress: onReceiveProgress);
    } on DioException catch (e) {
      logger.debug('Exception thrown : ${e.toString()}');
      throw AppException(exception: e);
    }

    return response;
  }
}
