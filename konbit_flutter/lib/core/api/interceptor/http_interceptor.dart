import 'dart:io';

import 'package:dio/dio.dart';
import 'package:uuid/uuid.dart';

import '../../logger/logger.dart';

class DefaultInterceptor extends Interceptor {
  DefaultInterceptor._(String traceId) : _traceId = traceId;

  String _traceId;

  static DefaultInterceptor? _defaultInterceptor;

  factory DefaultInterceptor.getSingleInstance() {
    _defaultInterceptor ??= DefaultInterceptor._(const Uuid().v7());

    return _defaultInterceptor!;
  }

  String get traceId => _traceId;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    String traceHeaderName = 'x-client-trace-id';
    options.headers.putIfAbsent(traceHeaderName, () => traceId);
    options.headers
        .putIfAbsent(HttpHeaders.userAgentHeader, () => 'KonbitFlutterApp');
    //debugPrint('$runtimeType adds Header $traceHeaderName = $traceId');
    logger.debug('$runtimeType Headers : ${options.headers}');
    super.onRequest(options, handler);
  }
}

class AccessTokenInterceptor extends Interceptor {
  AccessTokenInterceptor._();

  factory AccessTokenInterceptor.getSingleInstance() {
    _defaultInterceptor ??= AccessTokenInterceptor._();

    return _defaultInterceptor!;
  }

  static AccessTokenInterceptor? _defaultInterceptor;
  String? accessToken;

  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    logger.info('AccessTokenInterceptor - Access Token : $accessToken');
    if (accessToken != null) {
      final authTokenValue = 'Bearer $accessToken';
      options.headers.update(
          HttpHeaders.authorizationHeader, (value) => authTokenValue,
          ifAbsent: () => authTokenValue);
      //HttpHeaders.authorizationHeader, () => 'Bearer $_accessToken');
    }

    super.onRequest(options, handler);
  }
}
