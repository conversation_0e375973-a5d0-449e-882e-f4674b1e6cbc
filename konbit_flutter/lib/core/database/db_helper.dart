import 'dart:io';

import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

import '../config/konbit_config.dart';

const tokenTableName = 'token';
const String tblSqlScript = '''
  CREATE TABLE $tokenTableName(
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    token_name NVARCHAR(255) UNIQUE, 
    access_token TEXT,
    refresh_token TEXT, 
    created_at INTEGER, 
    token_expired_at INTEGER, 
    refresh_expired_at INTEGER)
''';

Future<Database> getDatabase(
    {String? basePath, String fileName = 'konbit.db'}) async {
  String dbBasePath = basePath ?? await getDatabasesPath();
  String dbFile = join(dbBasePath, fileName);
  logger.info('Database file path : $dbFile');
  var database = openDatabase(
    dbFile,
    onCreate: (db, version) {
      db.execute(tblSqlScript);
    },
    version: 1,
  );

  return database;
}

Future<Database> getGeoDatabase(
    {String? basePath, String dbFileName = GEO_DB_FILENAME}) async {
  String dbBasePath = basePath ?? await getDatabasesPath();
  String dbFile = join(dbBasePath, dbFileName);
  if (!File(dbFile).existsSync()) {
    throw Exception("GeoDatabase file not found : $dbFile");
  }
  logger.info('Opening Database file : $dbFile');
  return openDatabase(
    dbFile,
    onOpen: (db) {
      logger.info('Geo Database file opened successfully $dbFile');
    },
  );
}

String buildSqlSearchPattern(String search) {
  // Let's replace 'e' with or without accent with sql search pattern to
  // to ease sqlite collation feature
  final String partialSearch = search
      .toLowerCase()
      .replaceAll(' ', '%')
      .replaceAll(RegExp(r'[ioeé]'), '_')
      .replaceAll("-", '%');

  String completeSearch = "$partialSearch%";
  //remove double '%' at the beginning of search string
  if (partialSearch.substring(0, 1) == "%") {
    completeSearch = completeSearch.substring(1);
  }

  //remove double '%' at the end of search string
  if (partialSearch.substring(partialSearch.length - 1) == "%") {
    completeSearch = completeSearch.substring(0, completeSearch.length - 1);
  }

  return completeSearch;
}
