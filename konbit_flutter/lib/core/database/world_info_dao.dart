import 'package:konbit_flutter/core/database/db_helper.dart';
import 'package:konbit_flutter/core/model/db_entity/country.dart';
import 'package:konbit_flutter/core/model/db_entity/state.dart';
import 'package:sqflite/sqflite.dart';

import '../model/db_entity/city.dart';

class WorldInfoDao {
  WorldInfoDao({required Database database}) : _db = database;
  final Database _db;
  final String _queryCity = '''
    SELECT cities.name, states.name as state_name, states.iso2 as state_code, 
    countries.name as country_name, countries.iso3 as country_code, countries.emoji as country_flag
    FROM cities, countries, states
    WHERE cities.country_id = countries.id
        AND cities.state_id = states.id
        AND states.country_id = countries.id
	    AND lower(cities.name) LIKE ?
	 ORDER by countries.name, state_name, cities.name
  ''';

  final String _queryCityAndState = '''
    SELECT cities.name, states.name as state_name, states.iso2 as state_code,
    countries.name as country_name, countries.iso3 as country_code, countries.emoji as country_flag
    FROM cities, countries, states
    WHERE cities.country_id = countries.id
        AND cities.state_id = states.id
        AND states.country_id = countries.id
	    AND lower(cities.name) LIKE ?
	    AND lower(states.name) LIKE ?
	ORDER by countries.name, state_name, cities.name
  ''';

  final String _queryCityAndCountry = '''
    SELECT cities.name, states.name as state_name, states.iso2 as state_code, 
    countries.name as country_name, countries.iso3 as country_code, countries.emoji as country_flag
    FROM cities, countries, states
    WHERE cities.country_id = countries.id
        AND cities.state_id = states.id
        AND states.country_id = countries.id
	    AND lower(cities.name) LIKE ?
	   AND lower(countries.name) LIKE ? 
	ORDER by countries.name, state_name, cities.name
  ''';

  final String _queryCityStateCountry = '''
    SELECT cities.name, states.name as state_name, states.iso2 as state_code,
    countries.name as country_name, countries.iso3 as country_code, countries.emoji as country_flag
    FROM cities, countries, states
    WHERE cities.country_id = countries.id
        AND cities.state_id = states.id
        AND states.country_id = countries.id
	    AND lower(cities.name) LIKE ?
	    AND lower(states.name) LIKE ?
	    AND lower(countries.name) LIKE ?
	ORDER by countries.name, state_name, cities.name
  ''';

  final String _queryState = '''
    SELECT states.name as state_name, states.iso2 as state_code,
    countries.name as country_name, countries.iso3 as country_code, countries.emoji as country_flag
    FROM countries, states
    WHERE 
      states.country_id = countries.id
	    AND lower(states.name) LIKE ?
	ORDER by countries.name, state_name
  ''';

  final String _queryStateAndCountry = '''
    SELECT states.name as state_name, states.iso2 as state_code,
    countries.name as country_name, countries.iso3 as country_code, countries.emoji as country_flag
    FROM countries, states
    WHERE 
      states.country_id = countries.id
	    AND lower(states.name) LIKE ?
	    AND lower(countries.name) LIKE ?
	ORDER by countries.name, state_name
  ''';

  final String _queryCountry = '''
    SELECT countries.name as country_name, countries.iso3 as country_code, countries.emoji as country_flag
    FROM countries
    WHERE 
	    lower(countries.name) LIKE ?
	  ORDER by countries.name
  ''';

  final String _queryAllCountries = '''
    SELECT countries.name as country_name, countries.iso3 as country_code, countries.emoji as country_flag
    FROM countries
	  ORDER by countries.name
  ''';

  /// this method is used to build sql search strings for 'Like' operator
  Future<List<City>> cityLookup(String cityName,
      {String? stateName, String? countryName}) async {
    late List<Map<String, Object?>> queryResult;

    //Here we search by city only
    if (stateName == null && countryName == null) {
      queryResult =
          await _db.rawQuery(_queryCity, [buildSqlSearchPattern(cityName)]);
    }

    if (stateName != null && countryName == null) {
      queryResult = await _db.rawQuery(_queryCityAndState,
          [buildSqlSearchPattern(cityName), buildSqlSearchPattern(stateName)]);
    }

    if (stateName == null && countryName != null) {
      queryResult = await _db.rawQuery(_queryCityAndCountry, [
        buildSqlSearchPattern(cityName),
        buildSqlSearchPattern(countryName)
      ]);
    }

    if (stateName != null && countryName != null) {
      queryResult = await _db.rawQuery(_queryCityStateCountry, [
        buildSqlSearchPattern(cityName),
        buildSqlSearchPattern(stateName),
        buildSqlSearchPattern(countryName)
      ]);
    }

    return <City>[
      for (final {
            'name': city as String,
            'state_name': state as String,
            'state_code': stateCode as String,
            'country_name': country as String,
            'country_code': countryCode as String,
            'country_flag': countryFlag as String,
          } in queryResult)
        City(
            name: city,
            state: State(
                name: state,
                iso2: stateCode,
                country: Country(
                    name: country, iso3: countryCode, flag: countryFlag)),
            country:
                Country(name: country, iso3: countryCode, flag: countryFlag))
    ];
  }

  Future<List<State>> stateLookup(String stateName,
      [String? countryName]) async {
    late List<Map<String, Object?>> queryResult;

    //Here we search by city only
    if (countryName == null) {
      queryResult =
          await _db.rawQuery(_queryState, [buildSqlSearchPattern(stateName)]);
    }

    if (countryName != null) {
      queryResult = await _db.rawQuery(_queryStateAndCountry, [
        buildSqlSearchPattern(stateName),
        buildSqlSearchPattern(countryName)
      ]);
    }

    return <State>[
      for (final {
            'state_name': state as String,
            'state_code': stateCode as String,
            'country_name': country as String,
            'country_code': countryCode as String,
            'country_flag': countryFlag as String,
          } in queryResult)
        State(
            name: state,
            iso2: stateCode,
            country:
                Country(name: country, iso3: countryCode, flag: countryFlag))
    ];
  }

  Future<List<Country>> countryLookup(String countryName) async {
    List<Map<String, Object?>> queryResult =
        await _db.rawQuery(_queryCountry, [buildSqlSearchPattern(countryName)]);
    return <Country>[
      for (final {
            'country_name': country as String,
            'country_code': countryCode as String,
            'country_flag': countryFlag as String,
          } in queryResult)
        Country(name: country, iso3: countryCode, flag: countryFlag)
    ];
  }

  Future<List<Country>> findAllCountries() async {
    List<Map<String, Object?>> queryResult =
        await _db.rawQuery(_queryAllCountries);
    return <Country>[
      for (final {
            'country_name': country as String,
            'country_code': countryCode as String,
            'country_flag': countryFlag as String,
          } in queryResult)
        Country(name: country, iso3: countryCode, flag: countryFlag)
    ];
  }
}
