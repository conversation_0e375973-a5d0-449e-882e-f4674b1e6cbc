import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/core/model/db_entity/jwt_token_entity.dart';
import 'package:sqflite/sqflite.dart';

import '../config/konbit_config.dart';
import 'db_helper.dart';

class JwtTokenDao {
  const JwtTokenDao({required Database database}) : _db = database;

  final Database _db;

  Future<void> saveToken(JwtTokenEntity tokenEntity) async {
    try {
      await _db.insert(tokenTableName, tokenEntity.toMap(),
          conflictAlgorithm: ConflictAlgorithm.replace);
    } catch (e) {
      logger.error('Failed to insert ${tokenEntity.toMap()} with error : $e');
      rethrow;
    }

    logger.debug('Token persisted to sqlite3 db for ${tokenEntity.toString()}');
  }

  Future<JwtTokenEntity?> getToken() async {
    List<Map<String, Object?>> result = await _db.query(tokenTableName,
        where: 'token_name = ?', whereArgs: [JwtTokenEntity.TOKEN_NAME]);

    final List<JwtTokenEntity> tokens = <JwtTokenEntity>[
      for (final {
            'access_token': accessToken as String,
            'refresh_token': refreshToken as String,
            'created_at': createdAt as int,
            'token_expired_at': tokenExpiry as int,
            'refresh_expired_at': refreshTokenExpiry as int,
          } in result)
        JwtTokenEntity(
            tokenLifeCycle: tokenThreshold,
            refreshTokenLifeCycle: refreshTokenThreshold)
          ..accessToken = accessToken
          ..refreshToken = refreshToken
          ..tokenCreationDate =
              DateTime.fromMillisecondsSinceEpoch(createdAt, isUtc: true)
          ..accessTokenExpiry =
              DateTime.fromMillisecondsSinceEpoch(tokenExpiry, isUtc: true)
          ..refreshTokenExpiry = DateTime.fromMillisecondsSinceEpoch(
              refreshTokenExpiry,
              isUtc: true)
    ];

    if (tokens.length > 1) {
      throw JwtTokenDaoException(
          "Found more than 1 record for token name : ${JwtTokenEntity.TOKEN_NAME}");
    }

    return tokens.length == 1 ? tokens[0] : null;
  }

  Future<void> deleteToken() async {
    await _db.delete(tokenTableName);
  }
}

class JwtTokenDaoException implements Exception {
  JwtTokenDaoException(String message) : _message = message;
  final String _message;

  @override
  String toString() {
    return 'JwtTokenDaoException{_message: $_message}';
  }
}
