import 'dart:io';

import 'package:archive/archive.dart';
import 'package:flutter/services.dart';

import '../logger/logger.dart';

Future<File> unzipGeoAssetDb(String outFileName) async {
  final ByteData byteData =
      await rootBundle.load('packages/geo_resources/assets/world_data.db.bz2');
  BZip2Decoder bZip2Decoder = BZip2Decoder();

  final length = byteData.lengthInBytes;
  logger.info('Asset file size : $length bytes');

  final List<int> unzipData =
      bZip2Decoder.decodeBytes(byteData.buffer.asInt8List().toList());
  logger.info('Decoded Asset file size : ${unzipData.length} bytes');

  File dbFile = File(outFileName);
  logger.info('Database file : $dbFile');
  final dbFileWritten = await dbFile.writeAsBytes(unzipData);
  logger.info(
      '${dbFileWritten.absolute} size : ${dbFileWritten.lengthSync()} size');
  return dbFileWritten;
}
