import 'dart:io';

import 'package:beamer/beamer.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/config/konbit_config.dart';
import 'package:konbit_flutter/core/database/db_helper.dart';
import 'package:konbit_flutter/core/database/geo_asset_unziper.dart';
import 'package:konbit_flutter/core/helper/checksum_helper.dart';
import 'package:konbit_flutter/core/helper/shared_preferences_helper.dart';
import 'package:konbit_flutter/core/helper/token_helper.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/core/model/app_metadata.dart';
import 'package:konbit_flutter/core/model/drawer_info.dart';
import 'package:konbit_flutter/core/model/user_info.dart';
import 'package:konbit_flutter/core/theme/app_theme.dart';
import 'package:konbit_flutter/ui/google_map/map_initializer.dart';
import 'package:konbit_flutter/ui/router/app_router.dart';
import 'package:konbit_flutter/ui/widgets/app_bar_widget.dart';
import 'package:konbit_flutter/ui/widgets/progress_bar_widget.dart';
import 'package:path/path.dart';
import 'package:provider/provider.dart';
import 'package:sqflite/sqflite.dart';

import 'core/model/user_auth_data.dart';

void main() async {
  await MapInitializer.initializeIfAndroid();
  runApp(const MainApp());
}

class _AppResource {
  const _AppResource({required this.database, required this.userAuthData});

  final Database database;
  final UserAuthData userAuthData;
}

class MainApp extends StatefulWidget {
  const MainApp({super.key});

  @override
  State<MainApp> createState() => _MainAppState();
}

class _MainAppState extends State<MainApp> {
  final _routerDelegate = AppRouter.createDelegate();

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<_AppResource>(
      future: _getAppResource(),
      builder: (BuildContext context, AsyncSnapshot<_AppResource> snapshot) {
        if (snapshot.connectionState != ConnectionState.done) {
          return _buildLoadingScreen();
        }

        if (snapshot.hasError || !snapshot.hasData) {
          return _buildErrorScreen(snapshot.error.toString());
        }

        return _buildMainApp(snapshot.data!);
      },
    );
  }

  Future<Database> _extractGeoDatabase() async {
    final String dbBasePath = await getDatabasesPath();
    final dbFile = File(join(dbBasePath, GEO_DB_FILENAME));

    if (await _hasValidExistingDatabase(dbFile)) {
      return await getGeoDatabase();
    }

    await _recreateDatabase(dbFile);
    return await getGeoDatabase();
  }

  Future<bool> _hasValidExistingDatabase(File dbFile) async {
    if (!dbFile.existsSync()) return false;

    final assetCheckSum = await getDbAssetFileCheckSum();
    final dbFileCheckSum = sha256Checksum(dbFile.absolute.path);

    if (dbFileCheckSum == assetCheckSum) {
      logger.info('Existing database checksum matches embedded asset checksum');
      return true;
    }

    logger.info(
        'Database checksums differ - Asset: $assetCheckSum, File: $dbFileCheckSum');
    return false;
  }

  Future<void> _recreateDatabase(File dbFile) async {
    if (dbFile.existsSync()) {
      logger.info("Deleting ${dbFile.path}");
      dbFile.deleteSync();
    }
    await unzipGeoAssetDb(dbFile.absolute.path);
  }

  Future<_AppResource> _getAppResource() async {
    await loadKonbitConfig();
    final database = await _extractGeoDatabase();
    final userAuthData =
        await processAndRetrieveToken(cancelToken: CancelToken());
    return _AppResource(database: database, userAuthData: userAuthData);
  }

  Widget _buildLoadingScreen() {
    return MaterialApp(
      title: 'Konbit',
      theme: AppTheme.theme,
      home: Scaffold(
        appBar: AppBar(
          title: const Text('Konbit'),
        ),
        body: const Center(
          child: ProgressBarWidget(),
        ),
      ),
    );
  }

  Widget _buildErrorScreen(String error) {
    return MaterialApp(
      title: 'Konbit',
      theme: AppTheme.theme,
      home: Scaffold(
        appBar: AppBar(title: AppBarWidget()),
        body: Center(
          child: Text(
            'Failed to load Konbit due to errors. Please exit and relaunch app $error',
            style: const TextStyle(color: Colors.redAccent),
          ),
        ),
      ),
    );
  }

  Widget _buildMainApp(_AppResource appResource) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(
          create: (context) => AppMetadata(geoDatabase: appResource.database),
        ),
        ChangeNotifierProvider(
          create: (context) => appResource.userAuthData,
        ),
        ChangeNotifierProvider(
          create: (context) => UserInfo(),
        ),
        ChangeNotifierProvider(
          create: (context) => DrawerInfo(),
        )
      ],
      child: MaterialApp.router(
        title: 'Konbit',
        theme: AppTheme.theme,
        routerDelegate: _routerDelegate,
        routeInformationParser: BeamerParser(),
        backButtonDispatcher:
            BeamerBackButtonDispatcher(delegate: _routerDelegate),
      ),
    );
  }
}
