import 'package:beamer/beamer.dart';
import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/model/user_auth_data.dart';
import 'package:konbit_flutter/ui/pages/create_profile.dart';
import 'package:konbit_flutter/ui/pages/home.dart';
import 'package:konbit_flutter/ui/pages/upload_picture.dart';
import 'package:provider/provider.dart';

import '../../core/logger/logger.dart';

class HomeLocation extends BeamLocation<BeamState> {
  HomeLocation();

  @override
  List<Pattern> get pathPatterns =>
      ['/home', '/home/<USER>', '/home/<USER>'];

  @override
  List<BeamPage> buildPages(BuildContext context, BeamState state) {
    final authData = context.read<UserAuthData>();
    return [
      //HomePage
      if (authData.hasProfile())
        BeamPage(
          key: ValueKey('home-screen'),
          type: BeamPageType.fadeTransition,
          child: HomePage(),
          onPopPage: (context, delegate, state, poppedPage) {
            logger.info('HomePage onPopPage is called');
            return true;
          },
        ),
      //Create Profile Page
      if (state.uri.path == '/home/<USER>' || !authData.hasProfile())
        BeamPage(
            key: ValueKey('create-profile'),
            type: BeamPageType.noTransition,
            child: CreateProfilePage()),
      //Upload Picture Page
      if (state.uri.path.contains('upload_picture'))
        BeamPage(
            key: ValueKey('upload-picture'),
            type: BeamPageType.noTransition,
            child: UploadPicturePage(
              redirectPath: state.pathParameters['redirectPath'] ?? '/home',
            )),
    ];
  }
}
