import 'dart:core';

import 'package:reactive_forms/reactive_forms.dart';

Map<String, String Function(dynamic)> customValidationMessage(
    {String? fieldName, String? errorMessage}) {
  final fieldPrefix = fieldName ?? 'Field value';
  return {
    ValidationMessage.mustMatch: (error) =>
        errorMessage ?? "$fieldPrefix doesn't match",
    ValidationMessage.email: (error) =>
        errorMessage ?? "$fieldPrefix must be a valid email",
    ValidationMessage.required: (error) =>
        errorMessage ?? "$fieldPrefix must not be empty",
    ValidationMessage.minLength: (error) =>
        errorMessage ?? "$fieldPrefix  minimum length not met",
    ValidationMessage.maxLength: (error) =>
        errorMessage ?? "$fieldPrefix exceeds maximum length",
  };
}
