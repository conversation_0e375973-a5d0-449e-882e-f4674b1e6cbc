import 'package:reactive_forms/reactive_forms.dart';

class BlankStringValidator extends MinLengthValidator {
  BlankStringValidator(super.minLength);

  @override
  Map<String, dynamic>? validate(AbstractControl control) {
    if (control.value.runtimeType == String) {
      final String? valStr = control.value as String?;

      if (valStr == null) {
        return null;
      }

      if (valStr == '') {
        return null;
      }

      if (valStr.trim().isEmpty) {
        return {'Blank Not Valid': valStr};
      }

      if (valStr.trim().length < super.minLength) {
        return {'Minimum 2 characters required': valStr};
      }

      return super.validate(control);
    }

    return null;
  }
}
