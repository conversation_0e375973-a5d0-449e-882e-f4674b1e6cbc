import 'dart:async';

import 'package:beamer/beamer.dart';
import 'package:dio/dio.dart';
import 'package:draggable_float_widget/draggable_float_widget.dart';
import 'package:flutter/material.dart';
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:konbit_flutter/core/helper/google_maps_helper.dart';
import 'package:konbit_flutter/core/helper/token_helper.dart';
import 'package:konbit_flutter/core/model/output/user_profile_search.dart';
import 'package:konbit_flutter/core/model/user_auth_data.dart';
import 'package:konbit_flutter/ui/custom_validators/blank_string_validator.dart';
import 'package:konbit_flutter/ui/screens/components/home_map_manager.dart';
import 'package:konbit_flutter/ui/screens/components/home_screen_helpers.dart';
import 'package:konbit_flutter/ui/screens/components/profile_tile.dart';
import 'package:konbit_flutter/ui/screens/components/search_form_widget.dart';
import 'package:konbit_flutter/ui/screens/error_screen.dart';
import 'package:konbit_flutter/ui/services/profile_search_service.dart';
import 'package:konbit_flutter/ui/widgets/gmap_widget.dart';
import 'package:konbit_flutter/ui/widgets/progress_bar_widget.dart';
import 'package:konbit_flutter/ui/widgets/sliding_widget.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:reactive_forms/reactive_forms.dart';
import 'package:sheet/sheet.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key, required SheetController sheetController})
      : _sheetController = sheetController;
  final SheetController _sheetController;

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final _cancelToken = CancelToken();
  final _form = FormGroup({
    'skill': FormControl<String>(validators: [BlankStringValidator(2)]),
    'distance': FormControl<double>(value: 20.0),
    'max_item': FormControl<int>(value: 5),
  });

  bool _isDataLoading = true;

  bool _infoWindowOpened = false;

  late final HomeMapManager _mapManager;
  late final ProfileSearchService _searchService;

  final ScrollController _scrollController = ScrollController();

  Position? _currentPosition;
  List<UserProfileSummary> _profiles = [];

  @override
  void initState() {
    super.initState();

    final defaultMarkerWithHue =
        BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueViolet);

    _mapManager = HomeMapManager(
      defaultMarkerIcon: defaultMarkerWithHue,
      onProfileSelected: (profile) => _enableWindowInfo(),
      onInfoWindowClosed: foldSlidingWindow,
    );

    _searchService = ProfileSearchService(cancelToken: _cancelToken);

    WidgetsBinding.instance.addPostFrameCallback((_) async {
      _loadSearchResult();
    });
  }

  @override
  void dispose() {
    _cancelToken.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final containerSize = MediaQuery.sizeOf(context);
    final authData = Provider.of<UserAuthData>(context);
    final String username = authData.token != null
        ? getUsernameFromToken(authData.token!)
        : 'unknown';

    // Early return for error cases
    if (username == 'unknown') {
      return const SizedBox();
    }

    if (authData.userProfile == null) {
      return _buildErrorScreen(username);
    }

    final cameraPosition = _currentPosition != null
        ? LatLng(_currentPosition!.latitude, _currentPosition!.longitude)
        : LatLng(45.562329009768106, -73.60193309728906);

    // Determine if we're in portrait mode on a smaller screen

    return _buildMainStack(
      containerSize: containerSize,
      isPortraitSmallScreen: HomeScreenHelpers.isPortraitSmallScreen(context),
      cameraPosition: cameraPosition,
    );
  }

  void _enableWindowInfo() => setState(() => _infoWindowOpened = true);

  Future<void> _loadSearchResult() async {
    await _loadSearchData(
      onGpsPermissionError: (errorMessage) =>
          _showErrorDialog('GPS Permission Error', errorMessage),
      onUserCredentialError: (errorMessage) =>
          _showErrorDialog('User session Error', errorMessage),
      onDataFetchError: (errorMessage) =>
          _showErrorDialog('Data Loading Error', errorMessage),
    );
  }

  Future<void> _showErrorDialog(String title, String errorMessage) async {
    if (context.mounted) {
      await QuickAlert.show(
        context: context,
        title: title,
        type: QuickAlertType.error,
        text: errorMessage,
      );
    }
  }

  Future<void> _loadSearchData({
    void Function(String errorMessage)? onGpsPermissionError,
    void Function(String errorMessage)? onUserCredentialError,
    void Function(String errorMessage)? onDataFetchError,
  }) async {
    final authData = Provider.of<UserAuthData>(context, listen: false);

    // Step 1: Get user position
    if (!await _loadAndUpdateCurrentUserLocation(
        onGpsPermissionError: onGpsPermissionError)) {
      _setDataLoadingFlag(false);
      return;
    }

    // Step 2: Move camera to user position
    await moveCameraToPosition(
      _mapManager.mapController,
      LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
      defaultZoom,
    );

    // Step 3: Fetch user data
    if (!await _loadOrUpdateUserAuthData(authData, onUserCredentialError)) {
      _setDataLoadingFlag(false);
      return;
    }

    // Step 4: Search profiles
    await _searchAndPopulateProfiles(onDataFetchError);
    _infoWindowOpened = false;
    _setDataLoadingFlag(false);
  }

  Future<bool> _loadAndUpdateCurrentUserLocation({
    void Function(String errorMessage)? onGpsPermissionError,
  }) async {
    try {
      _currentPosition = await _searchService.getCurrentUserPosition();
      return true;
    } catch (ex) {
      if (onGpsPermissionError != null) {
        onGpsPermissionError(ex.toString());
      }
      return false;
    }
  }

  Future<bool> _loadOrUpdateUserAuthData(
    UserAuthData authData,
    void Function(String errorMessage)? onUserCredentialError,
  ) async {
    try {
      final userData = await _searchService.fetchOrUpdateUserAuthData(
        authData: authData,
        onTokenMissing: () {
          authData.updateUserData(UserAuthData());
          context.beamToNamed('/');
        },
        onProfileMissing: (token) => context.beamToNamed('/create_profile'),
      );
      authData.updateUserData(userData);
      return true;
    } catch (ex) {
      if (onUserCredentialError != null) {
        onUserCredentialError(ex.toString());
      }
      return false;
    }
  }

  Future<void> _searchAndPopulateProfiles(
    void Function(String errorMessage)? onDataFetchError,
  ) async {
    try {
      _profiles = await _searchService.searchProfiles(
        skill: _form.controls['skill']?.value as String?,
        maxDistance:
            (_form.controls['distance']!.value as double).floor() * 1000,
        maxItems: _form.controls['max_item']!.value as int,
        currentPosition: _currentPosition!,
      );

      await _mapManager.initializeProfileMarkers(
        currentPosition:
            LatLng(_currentPosition!.latitude, _currentPosition!.longitude),
        profiles: _profiles,
      );
    } catch (ex) {
      if (onDataFetchError != null) {
        onDataFetchError(ex.toString());
      }
    }
  }

  void _setDataLoadingFlag(bool isLoading) {
    setState(() {
      _isDataLoading = isLoading;
    });
  }

  void _submitSearch(formControl) async {
    _form.markAllAsTouched();
    if (formControl.valid && !_form.controls['skill']!.hasErrors) {
      _setDataLoadingFlag(true);
      await _loadSearchResult();
    }
  }

  String _buildNoResultMessage() {
    String? searchValue = _form.control('skill').value as String?;
    return HomeScreenHelpers.buildNoResultMessage(
      searchValue: searchValue,
      distanceMeter: _form.control('distance').value as double?,
    );
  }

  void _onTapProfileTile(UserProfileSummary userProfileSummary) async {
    final targetPosition =
        LatLng(userProfileSummary.latitude, userProfileSummary.longitude);

    final zoomLevel = _mapManager.mapController != null
        ? await _mapManager.mapController.getZoomLevel()
        : defaultZoom;

    await moveCameraToPosition(
        _mapManager.mapController, targetPosition, zoomLevel);

    _mapManager.highlightSelectedProfileMarker(userProfileSummary);
  }

  void foldSlidingWindow() {
    widget._sheetController.animateTo(30.0,
        duration: Duration(milliseconds: 500), curve: Curves.easeInOutCubic);
  }

  Widget _buildErrorScreen(String username) {
    return ErrorScreen(
      message: '$username has no profile. Tap below to create profile',
      icon: Icons.account_circle,
      onPressed: () => context.beamToNamed('/create_profile'),
    );
  }

  Widget _buildMainStack({
    required Size containerSize,
    required bool isPortraitSmallScreen,
    required LatLng cameraPosition,
  }) {
    return Stack(
      alignment: AlignmentDirectional.topStart,
      fit: StackFit.loose,
      children: [
        // Map section
        _buildMapSection(
          containerSize: containerSize,
          isPortraitSmallScreen: isPortraitSmallScreen,
          cameraPosition: cameraPosition,
        ),

        // Profile floating widget
        if (_mapManager.currentSelectedProfile != null)
          _ProfileFloatingWidget(
            profile: _mapManager.currentSelectedProfile!,
            onTap: () => setState(() {
              _mapManager.clearCurrentSelectedProfile();
            }),
            bottomBorder:
                isPortraitSmallScreen ? containerSize.height * 0.4 : 90,
          ),

        // Sliding panel
        _buildSlidingPanel(
          containerSize: containerSize,
          isPortraitSmallScreen: isPortraitSmallScreen,
        ),

        // Loading indicator
        if (_isDataLoading)
          const Positioned.fill(
            child: Center(
              child: ProgressBarWidget(
                label: "Loading data...",
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildMapSection({
    required Size containerSize,
    required bool isPortraitSmallScreen,
    required LatLng cameraPosition,
  }) {
    if (isPortraitSmallScreen) {
      return SizedBox(
        width: containerSize.width,
        height: containerSize.height * 0.638,
        child: _MapSection(
          cameraPosition: cameraPosition,
          mapManager: _mapManager,
        ),
      );
    } else {
      return _MapSection(
        cameraPosition: cameraPosition,
        mapManager: _mapManager,
      );
    }
  }

  Widget _buildSlidingPanel({
    required Size containerSize,
    required bool isPortraitSmallScreen,
  }) {
    return Positioned(
      width: isPortraitSmallScreen ? null : containerSize.width * 0.45,
      height: isPortraitSmallScreen ? null : containerSize.height,
      child: _SlidingPanel(
        sheetController: widget._sheetController,
        initialExtent: containerSize.height * 0.20,
        minExtent: containerSize.height * 0.28,
        form: _form,
        onSubmitted: _submitSearch,
        profiles: _profiles,
        isDataLoading: _isDataLoading,
        buildNoResultMessage: _buildNoResultMessage,
        onTapProfileTile: _onTapProfileTile,
        scrollController: isPortraitSmallScreen ? _scrollController : null,
        formKey: widget.key,
      ),
    );
  }
}

class _FormControlWidget extends StatefulWidget {
  const _FormControlWidget(
      {super.key,
      required FormGroup form,
      void Function(FormControl<dynamic>)? onSubmitted})
      : _form = form,
        _onSubmitted = onSubmitted;

  final FormGroup _form;
  final void Function(FormControl<dynamic>)? _onSubmitted;

  @override
  State<_FormControlWidget> createState() => _FormControlWidgetState();
}

class _FormControlWidgetState extends State<_FormControlWidget> {
  @override
  Widget build(BuildContext context) {
    return SearchFormWidget(
      form: widget._form,
      onSubmitted: widget._onSubmitted,
    );
  }
}

// Extract the map section into a separate widget
class _MapSection extends StatelessWidget {
  const _MapSection({
    required this.cameraPosition,
    required this.mapManager,
  });

  final LatLng cameraPosition;
  final HomeMapManager mapManager;

  @override
  Widget build(BuildContext context) {
    return GMapWidget(
      cameraCoords: cameraPosition,
      clusterManagers: mapManager.clusterManagers,
      markers: mapManager.markers,
      zoom: defaultZoom,
      onMapCreated: mapManager.onMapCreated,
    );
  }
}

// Extract the profile floating widget into a separate widget
class _ProfileFloatingWidget extends StatelessWidget {
  const _ProfileFloatingWidget({
    required this.profile,
    required this.onTap,
    this.bottomBorder = 90,
  });

  final UserProfileSummary profile;
  final VoidCallback onTap;
  final double bottomBorder;

  @override
  Widget build(BuildContext context) {
    return DraggableFloatWidget(
      width: 450,
      height: 300,
      config: DraggableFloatWidgetBaseConfig(
          borderLeft: 20, borderRight: 20, borderBottom: bottomBorder),
      child: ProfileTile(
        userProfileSummary: profile,
        onTap: onTap,
      ),
    );
  }
}

// Extract the sliding panel into a separate widget
class _SlidingPanel extends StatelessWidget {
  const _SlidingPanel({
    required this.sheetController,
    required this.initialExtent,
    required this.minExtent,
    required this.form,
    required this.onSubmitted,
    required this.profiles,
    required this.isDataLoading,
    required this.buildNoResultMessage,
    required this.onTapProfileTile,
    required this.scrollController,
    required this.formKey,
  });

  final SheetController sheetController;
  final double initialExtent;
  final double minExtent;
  final FormGroup form;
  final Function(FormControl<dynamic>) onSubmitted;
  final List<UserProfileSummary> profiles;
  final bool isDataLoading;
  final String Function() buildNoResultMessage;
  final Function(UserProfileSummary) onTapProfileTile;
  final ScrollController? scrollController;
  final Key? formKey;

  @override
  Widget build(BuildContext context) {
    const slidingWidgetTitle = Wrap(
      children: [
        Text('Search for people with skills'),
      ],
    );

    return SlidingWidget(
      sheetController: sheetController,
      title: slidingWidgetTitle,
      initialExtent: initialExtent,
      minExtent: minExtent,
      child: CustomScrollView(
        controller: scrollController,
        shrinkWrap: true,
        physics: BouncingScrollPhysics(),
        slivers: [
          _FormControlWidget(
            key: formKey,
            form: form,
            onSubmitted: onSubmitted,
          ),
          if (profiles.isEmpty && !isDataLoading)
            _NoResultSliverBox(message: buildNoResultMessage()),
          for (final (index, profileSummary) in profiles.indexed)
            SliverToBoxAdapter(
              child: Padding(
                padding: index == 0
                    ? const EdgeInsets.only(bottom: 20.0, top: 6.0)
                    : const EdgeInsets.only(bottom: 20.0),
                child: ProfileTile(
                  userProfileSummary: profileSummary,
                  onTap: () => onTapProfileTile(profileSummary),
                ),
              ),
            ),
        ],
      ),
    );
  }
}

class _NoResultSliverBox extends StatelessWidget {
  final String message;

  const _NoResultSliverBox({required this.message});

  @override
  Widget build(BuildContext context) {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.only(top: 8.0),
        child: Center(
          child: Text(
            message,
            style: TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.red.shade400,
                fontSize: 20.0),
          ),
        ),
      ),
    );
  }
}
