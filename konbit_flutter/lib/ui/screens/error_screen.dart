import 'package:flutter/material.dart';

class ErrorScreen extends StatelessWidget {
  const ErrorScreen({
    super.key,
    required String message,
    IconData icon = Icons.refresh,
    required void Function() onPressed,
  })  : _message = message,
        _iconData = icon,
        _onPressed = onPressed;
  final String _message;
  final void Function() _onPressed;
  final IconData _iconData;

  @override
  Widget build(BuildContext context) {
    return Card(
      color: Colors.red.shade100,
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline_outlined),
                const SizedBox(
                  width: 5.0,
                ),
                Text(_message),
              ],
            ),
            const SizedBox(
              height: 16.0,
            ),
            IconButton.outlined(onPressed: _onPressed, icon: Icon(_iconData))
          ],
        ),
      ),
    );
  }
}
