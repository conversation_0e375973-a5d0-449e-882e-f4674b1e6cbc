import 'dart:io';

import 'package:camerawesome/camerawesome_plugin.dart';
import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;
import 'package:path_provider/path_provider.dart';

import '../../core/logger/logger.dart';

class TakePictureScreen extends StatefulWidget {
  const TakePictureScreen({super.key, required this.onPictureTaken});

  final void Function(File) onPictureTaken;

  @override
  State<TakePictureScreen> createState() => _TakePictureScreenState();
}

class _TakePictureScreenState extends State<TakePictureScreen> {
  @override
  Widget build(BuildContext context) {
    return CameraAwesomeBuilder.awesome(
      enablePhysicalButton: true,
      saveConfig: SaveConfig.photo(),
      onMediaTap: _onMediaCapture,
      onMediaCaptureEvent: _onMediaCapture,
    );
  }

  Future<void> _onMediaCapture(MediaCapture mediaCapture) async {
    final directory = await getApplicationDocumentsDirectory();
    final fileName = path.basename(mediaCapture.captureRequest.path!);
    final savedImage = await File(mediaCapture.captureRequest.path!)
        .copy('${directory.path}/$fileName');
    logger.info(
        'Saved ${mediaCapture.captureRequest.path} image to ${savedImage.path}');
    if (context.mounted) {
      widget.onPictureTaken(savedImage);
      Navigator.of(context).pop();
    }
  }
}
