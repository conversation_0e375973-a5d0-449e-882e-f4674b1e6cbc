import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/helper/distance_conversion_helper.dart';
import 'package:konbit_flutter/ui/widgets/form_component/textfield_component.dart';
import 'package:reactive_forms/reactive_forms.dart';

class SearchFormWidget extends StatefulWidget {
  const SearchFormWidget({
    super.key,
    required this.form,
    this.onSubmitted,
  });

  final FormGroup form;
  final void Function(FormControl<dynamic>)? onSubmitted;

  @override
  State<SearchFormWidget> createState() => _SearchFormWidgetState();
}

class _SearchFormWidgetState extends State<SearchFormWidget> {
  @override
  Widget build(BuildContext context) {
    final distance = (widget.form.control('distance').value as double) * 1000;

    return SliverAppBar(
      expandedHeight: 200,
      pinned: true,
      floating: false,
      title: ReactiveForm(
        formGroup: widget.form,
        child: Padding(
          padding: const EdgeInsets.only(top: 41.0, bottom: 60.0),
          child: TextFieldWidget(
            formControlName: 'skill',
            labelText: 'Type Occupation and Press Enter',
            hintText: 'Cook, Mechanic, Hair',
            suffixIcon: IconButton(
              icon: Icon(Icons.search_outlined),
              onPressed: () {
                if (widget.onSubmitted != null) {
                  widget.onSubmitted!(
                      widget.form.control('skill') as FormControl<dynamic>);
                }
              },
            ),
            onSubmitted: widget.onSubmitted,
          ),
        ),
      ),
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          color: Colors.white,
          child: ReactiveForm(
            formGroup: widget.form,
            child: Padding(
              padding: const EdgeInsets.only(left: 10.0, top: 60.0),
              child: Column(
                children: [
                  Row(
                    children: [
                      Text(
                        getReadableDistance(distance, DistanceUnit.kilometer),
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                      Expanded(
                        child: ReactiveSlider(
                          formControlName: 'distance',
                          min: 1,
                          max: 100,
                          labelBuilder: (value) => value.round().toString(),
                          onChanged: (_) => setState(() {}),
                          onChangeEnd: widget.onSubmitted,
                        ),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Text(
                        'Max Items : ${widget.form.controls['max_item']!.value as int}',
                        style: TextStyle(fontWeight: FontWeight.w500),
                      ),
                      Expanded(
                        child: ReactiveSlider(
                          formControlName: 'max_item',
                          min: 1,
                          max: 100,
                          labelBuilder: (value) => value.round().toString(),
                          onChanged: (_) => setState(() {}),
                          onChangeEnd: widget.onSubmitted,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
