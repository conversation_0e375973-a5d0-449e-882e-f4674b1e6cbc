import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/helper/distance_conversion_helper.dart';
import 'package:quiver/strings.dart';

/// Helper functions for the HomeScreen
class HomeScreenHelpers {
  /// Builds a message for when no search results are found
  static String buildNoResultMessage({
    required String? searchValue,
    required double? distanceMeter,
  }) {
    String noResultMsg = isNotBlank(searchValue)
        ? 'Nobody found with skill $searchValue'
        : 'Nobody found';

    noResultMsg = distanceMeter != null
        ? '$noResultMsg at ${getReadableDistance(distanceMeter * 1000, DistanceUnit.kilometer)}'
        : noResultMsg;

    noResultMsg +=
        "\nModify either your search keyword or increase the distance";

    return noResultMsg;
  }

  /// Determines if the screen is in portrait mode on a small device
  static bool isPortraitSmallScreen(BuildContext context) {
    final containerSize = MediaQuery.sizeOf(context);
    final screenOrientation = MediaQuery.orientationOf(context);

    return containerSize.width < 900.0 &&
        screenOrientation == Orientation.portrait;
  }
}
