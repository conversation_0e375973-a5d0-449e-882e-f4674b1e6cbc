import 'package:collection/collection.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

import '../../../core/helper/distance_conversion_helper.dart';
import '../../../core/logger/logger.dart';
import '../../../core/model/output/user_profile_search.dart';

class MapControllerHelper {
  final BitmapDescriptor defaultMarkerIcon;
  GoogleMapController? mapController;
  UserProfileSummary? _currentSelectedProfile;

  final _clusterManagers = <ClusterManager>{};
  final _markers = <Marker>{};

  MapControllerHelper({required this.defaultMarkerIcon});

  get clusterManagers => _clusterManagers;

  get markers => _markers;

  UserProfileSummary? get currentSelectedProfile => _currentSelectedProfile;

  void onMapCreated(GoogleMapController controller) {
    mapController = controller;
  }

  Future<void> moveCameraToPosition(LatLng position, double zoom) async {
    if (mapController != null) {
      await mapController!.animateCamera(
        CameraUpdate.newLatLngZoom(position, zoom),
      );
    }
  }

  void highlightSelectedProfileMarker(UserProfileSummary selectedProfile,
      {required void Function(UserProfileSummary userProfile)
          onMarkerSelected}) {
    logger.debug('Tapping on marker: ${selectedProfile.username}');

    _clearPreviousSelectedMarker();

    final selectedMarker = _findMarkerByUsername(selectedProfile.username!);

    //Stop processing further if marker  not found
    if (selectedMarker == null) {
      return;
    }

    const double selectedHue = 340;
    _markers.remove(selectedMarker);
    final updatedMarker = selectedMarker.copyWith(
        iconParam: BitmapDescriptor.defaultMarkerWithHue(selectedHue));
    _markers.add(updatedMarker);

    _currentSelectedProfile = selectedProfile;
    onMarkerSelected(selectedProfile);
  }

  Marker? _findMarkerByUsername(String username) {
    return _markers.firstWhereOrNull(
      (element) => element.markerId == MarkerId(username),
    );
  }

  void clearCurrentSelectedProfile() {
    _currentSelectedProfile = null;
  }

  void _clearAllMarkers() {
    _markers.clear();
  }

  void initMarkersForProfiles(
      {required LatLng currentPosition,
      required List<UserProfileSummary> profiles,
      int clusterSize = 20,
      required void Function(int index, UserProfileSummary profileSummary)
          onTapMarker}) {
    _clearAllMarkers();

    addMarkerForCurrentLocation(currentPosition);

    final totalElementsByCluster = clusterSize.clamp(1, 50);
    int clusterIndex = 0;

    ClusterManager? clusterManager;

    for (final (index, profileSummary) in profiles.indexed) {
      if (index >= totalElementsByCluster &&
          index % totalElementsByCluster == 0) {
        clusterIndex++;
        clusterManager = ClusterManager(
            clusterManagerId: ClusterManagerId("Cluster$clusterIndex"));
        _clusterManagers.add(clusterManager);
      }

      _addMarkerForProfile(profileSummary, clusterManager, onTapMarker, index);
    }
  }

  void _addMarkerForProfile(
      UserProfileSummary profileSummary,
      ClusterManager? clusterManager,
      void Function(int index, UserProfileSummary profileSummary) onTapMarker,
      int index) {
    final distance =
        getReadableDistance(profileSummary.distance!, DistanceUnit.kilometer);

    final marker = Marker(
      clusterManagerId: clusterManager?.clusterManagerId,
      markerId: MarkerId(profileSummary.username!),
      position: LatLng(profileSummary.latitude, profileSummary.longitude),
      icon: defaultMarkerIcon,
      infoWindow: InfoWindow(
          title: profileSummary.username!,
          snippet:
              "occupation: ${profileSummary.mainOccupation} distance: $distance"),
      onTap: () => onTapMarker(index, profileSummary),
    );
    _markers.add(marker);
  }

  void addMarkerForCurrentLocation(LatLng currentPosition) {
    _markers.add(Marker(
        markerId: MarkerId('Your Location'),
        position: LatLng(currentPosition.latitude, currentPosition.longitude),
        icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueAzure),
        infoWindow: InfoWindow(
            title: 'Your Location', snippet: currentPosition.toString())));
  }

  void _clearPreviousSelectedMarker() {
    final previousSelectedMarker = (_currentSelectedProfile != null)
        ? _findMarkerByUsername(_currentSelectedProfile!.username!)
        : null;

    if (previousSelectedMarker != null) {
      _markers.remove(previousSelectedMarker);
      _markers
          .add(previousSelectedMarker.copyWith(iconParam: defaultMarkerIcon));
    }
  }
}
