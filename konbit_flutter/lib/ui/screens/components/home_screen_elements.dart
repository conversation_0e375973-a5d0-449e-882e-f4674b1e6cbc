import 'package:flutter/material.dart';

class ProfileTileView extends StatefulWidget {
  const ProfileTileView({super.key});

  @override
  State<ProfileTileView> createState() => _ProfileTileViewState();
}

class _ProfileTileViewState extends State<ProfileTileView> {
  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(builder: (context, constraints) {
      return Padding(
        padding: EdgeInsets.only(bottom: 5.0),
        child: <PERSON><PERSON>B<PERSON>(
          width: constraints.widthConstraints().biggest.width,
          height: constraints.widthConstraints().smallest.height,
          child: Container(
            color: Colors.indigo,
          ),
        ),
      );
    });
  }
}
