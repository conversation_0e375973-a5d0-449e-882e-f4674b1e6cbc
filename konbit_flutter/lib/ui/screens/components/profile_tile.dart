import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/helper/country_flag_helper.dart';
import 'package:konbit_flutter/core/helper/distance_conversion_helper.dart';
import 'package:konbit_flutter/core/model/output/user_profile_search.dart';
import 'package:path/path.dart';

import '../../../core/api/profile_picture_api.dart';
import '../../widgets/network_image_widget.dart';

class ProfileTile extends StatefulWidget {
  const ProfileTile(
      {super.key,
      required UserProfileSummary userProfileSummary,
      void Function()? onTap,
      void Function(BuildContext context)? onProfileTileCreated})
      : _userProfileSummary = userProfileSummary,
        _onTap = onTap,
        _onProfileTileCreated = onProfileTileCreated;
  final UserProfileSummary _userProfileSummary;
  final void Function()? _onTap;
  final void Function(BuildContext context)? _onProfileTileCreated;

  @override
  State<ProfileTile> createState() => _ProfileTileState();
}

class _ProfileTileState extends State<ProfileTile>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation _colorTween;
  Color? _defaultColor = Colors.white;

  @override
  void initState() {
    _animationController =
        AnimationController(vsync: this, duration: Duration(milliseconds: 200));
    _colorTween = ColorTween(begin: Colors.white70, end: Colors.white)
        .animate(_animationController);
    super.initState();
  }

  @override
  void didChangeDependencies() {
    if (widget._onProfileTileCreated != null) {
      widget._onProfileTileCreated!(context as BuildContext);
    }
    super.didChangeDependencies();
  }

  @override
  Widget build(BuildContext context) {
    final widgetHeight = MediaQuery.sizeOf(context).height;
    final orientation = MediaQuery.orientationOf(context);
    final distanceStr = getReadableDistance(
        widget._userProfileSummary.distance!, DistanceUnit.kilometer);

    final countryEmoji =
        getCountryFlag(widget._userProfileSummary.countryCode!);

    final List<String> skills =
        widget._userProfileSummary.skills ?? List.of([]);
    skills.sort();

    return AnimatedBuilder(
      animation: _colorTween,
      builder: (context, child) => InkWell(
        onTap: () async {
          _defaultColor = null;
          _animationController.forward().whenComplete(
            () {
              _defaultColor = Colors.white;
              _animationController.reset();
            },
          );

          if (widget._onTap != null) {
            widget._onTap!();
          }
        },
        child: Padding(
          padding: EdgeInsets.only(left: 6.0, right: 6.0),
          child: Container(
            height: orientation == Orientation.portrait
                ? widgetHeight * 0.3
                : widgetHeight * 0.65,
            color: _defaultColor ?? _colorTween.value,
            child: Column(
              children: [
                SizedBox(
                  height: 6.0,
                ),
                ListTile(
                  /*leading: Icon(
                    Icons.account_circle,
                    size: 40.0,
                  ),*/
                  leading: CircleAvatar(
                    radius: 40.0,
                    child: NetworkImageWidget(
                      imageUriPath:
                          '${ProfilePictureApi.baseUri}/profile/${widget._userProfileSummary.username}',
                    ),
                  ),
                  title: Text('${widget._userProfileSummary.username}'),
                  trailing: Text(distanceStr),
                ),
                SizedBox(
                  height: 6.0,
                ),
                Flexible(
                  flex: 4,
                  fit: FlexFit.tight,
                  child: Container(
                      color: Colors.white12,
                      child: ListTile(
                        title: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Text(
                                  'Occupation: ',
                                  style: TextStyle(fontWeight: FontWeight.bold),
                                ),
                                Text(
                                  '${widget._userProfileSummary.mainOccupation}',
                                  style: TextStyle(
                                      color: Colors.black87,
                                      fontStyle: FontStyle.italic),
                                ),
                              ],
                            ),
                            SizedBox(
                              height: 5.0,
                            ),
                            Wrap(
                              spacing: 6.0,
                              alignment: WrapAlignment.start,
                              children: [
                                for (final String skill in skills)
                                  _SkillTag(
                                    key: widget.key,
                                    tag: skill,
                                    backgroundColor: Colors.lightBlue.shade300,
                                  )
                              ],
                            ),
                          ],
                        ),
                      )
                      //width: 100,
                      //height: 100,
                      ),
                ),
                Flexible(
                    flex: 1,
                    fit: FlexFit.loose,
                    child: Container(
                      color: Colors.white,
                      child: ListTile(
                        leading: Text(
                          '$countryEmoji ${widget._userProfileSummary.countryCode}',
                          style: TextStyle(fontSize: 20),
                        ),
                        trailing: Text(
                          '${widget._userProfileSummary.city}, ${widget._userProfileSummary.locality1}',
                          style: TextStyle(fontSize: 16),
                        ),
                      ),
                    ))
              ],
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }
}

class _SkillTag extends StatelessWidget {
  const _SkillTag({super.key, required String tag, Color? backgroundColor})
      : _tag = tag,
        _backgroundColor = backgroundColor;
  final String _tag;
  final Color? _backgroundColor;

  @override
  Widget build(BuildContext context) {
    return RawChip(
      label: Text(_tag),
      visualDensity: VisualDensity.adaptivePlatformDensity,
      backgroundColor: _backgroundColor,
    );
  }
}
