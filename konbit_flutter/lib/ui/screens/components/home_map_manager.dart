import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:konbit_flutter/core/model/output/user_profile_search.dart';
import 'package:konbit_flutter/ui/screens/components/map_controller_helper.dart';

class HomeMapManager {
  final MapControllerHelper mapControllerHelper;
  final Function(UserProfileSummary) onProfileSelected;
  final Function() onInfoWindowClosed;

  HomeMapManager({
    required BitmapDescriptor defaultMarkerIcon,
    required this.onProfileSelected,
    required this.onInfoWindowClosed,
  }) : mapControllerHelper =
            MapControllerHelper(defaultMarkerIcon: defaultMarkerIcon);

  get markers => mapControllerHelper.markers;

  get clusterManagers => mapControllerHelper.clusterManagers;

  get mapController => mapControllerHelper.mapController;

  get currentSelectedProfile => mapControllerHelper.currentSelectedProfile;

  void onMapCreated(GoogleMapController controller) {
    mapControllerHelper.onMapCreated(controller);
  }

  Future<void> initializeProfileMarkers({
    required LatLng currentPosition,
    required List<UserProfileSummary> profiles,
  }) async {
    mapControllerHelper.initMarkersForProfiles(
      currentPosition: currentPosition,
      profiles: profiles,
      onTapMarker: (_, profileSummary) {
        highlightSelectedProfileMarker(profileSummary);
        onInfoWindowClosed();
      },
    );
  }

  void highlightSelectedProfileMarker(UserProfileSummary profileSummary) {
    mapControllerHelper.highlightSelectedProfileMarker(
      profileSummary,
      onMarkerSelected: (_) => onProfileSelected(profileSummary),
    );
  }

  void clearSelectedProfile() {
    mapControllerHelper.clearCurrentSelectedProfile();
  }

  void clearCurrentSelectedProfile() {
    mapControllerHelper.clearCurrentSelectedProfile();
  }
}
