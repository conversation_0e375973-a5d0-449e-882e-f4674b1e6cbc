import 'package:beamer/beamer.dart';
import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/model/user_auth_data.dart';
import 'package:konbit_flutter/ui/pages/login.dart';
import 'package:konbit_flutter/ui/pages/main_page.dart';
import 'package:konbit_flutter/ui/pages/registration.dart';
import 'package:provider/provider.dart';

class AppRouter {
  static BeamerDelegate createDelegate() => BeamerDelegate(
        initialPath: '/',
        locationBuilder: RoutesLocationBuilder(
          routes: {
            '/': (context, state, object) {
              return !context.read<UserAuthData>().isAuthenticated()
                  ? BeamPage(
                      key: const ValueKey('startup-page'),
                      type: BeamPageType.noTransition,
                      child: const LoginPage())
                  : BeamPage(
                      type: BeamPageType.scaleTransition,
                      child: const MainPage());
            },
            '/login': (context, state, object) => BeamPage(
                key: const ValueKey('login-page'),
                type: BeamPageType.scaleTransition,
                child: const LoginPage()),
            '/register': (context, state, object) => BeamPage(
                key: const ValueKey('registration-page'),
                type: BeamPageType.scaleTransition,
                child: const RegistrationPage()),
            '/home': (context, state, object) => BeamPage(
                key: const ValueKey('main-page'),
                type: BeamPageType.scaleTransition,
                child: const MainPage()),
          },
        ).call,
        guards: <BeamGuard>[
          BeamGuard(
            pathPatterns: ['/home'],
            check: (context, location) =>
                Provider.of<UserAuthData>(context, listen: false)
                    .isAuthenticated(),
            beamToNamed: (origin, target) => '/',
          ),
        ],
      );
}
