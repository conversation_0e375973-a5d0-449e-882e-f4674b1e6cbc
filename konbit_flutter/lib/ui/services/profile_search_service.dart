import 'package:dio/dio.dart';
import 'package:geolocator/geolocator.dart';
import 'package:konbit_flutter/core/api/dio_builder.dart';
import 'package:konbit_flutter/core/api/interceptor/http_interceptor.dart';
import 'package:konbit_flutter/core/api/user_profile_api.dart';
import 'package:konbit_flutter/core/config/konbit_config.dart';
import 'package:konbit_flutter/core/helper/location_helper.dart';
import 'package:konbit_flutter/core/helper/token_helper.dart';
import 'package:konbit_flutter/core/model/input/query/location_query.dart';
import 'package:konbit_flutter/core/model/output/auth_location_query_result.dart';
import 'package:konbit_flutter/core/model/output/user_profile_search.dart';
import 'package:konbit_flutter/core/model/user_auth_data.dart';

class ProfileSearchService {
  final CancelToken cancelToken;

  ProfileSearchService({required this.cancelToken});

  Future<Position> getCurrentUserPosition() async {
    return await getCurrentPosition();
  }

  Future<UserAuthData> fetchOrUpdateUserAuthData({
    required UserAuthData authData,
    required Function() onTokenMissing,
    required Function(String) onProfileMissing,
    bool forceTokenRefresh = false,
  }) async {
    return await processAndRetrieveToken(
      cancelToken: cancelToken,
      onTokenMissing: onTokenMissing,
      onProfileMissing: onProfileMissing,
      forceRefresh: forceTokenRefresh,
    );
  }

  Future<List<UserProfileSummary>> searchProfiles({
    String? skill,
    double maxDistance = 20000.0,
    int maxItems = 5,
    required Position currentPosition,
  }) async {
    final profileApi = UserProfileApi(
        DioBuilder.create()
            .baseUrl(KonbitConfig.getConfig().getUrl())
            .interceptor(AccessTokenInterceptor.getSingleInstance())
            .build(),
        cancelToken);

    final locationQuery = LocationQuery(
        occupation: skill,
        latitude: currentPosition.latitude,
        longitude: currentPosition.longitude,
        maxDistance: maxDistance.floor(),
        limit: maxItems);

    final response = await profileApi.getProfilesByLocation(locationQuery);
    final AuthLocationQueryResult? locationQueryResult = response!.data;

    return locationQueryResult?.profiles ?? [];
  }
}
