import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';

import '../../core/logger/logger.dart';
import '../../core/model/drawer_info.dart';

class AppExitHandler {
  AppExitHandler(BuildContext context) : _context = context;
  final BuildContext _context;
  bool _dialogOpened = false;

  bool drawerClosedTriggered() {
    final opened = Provider.of<DrawerInfo>(_context, listen: false).isOpened;
    logger.info('PopeScope is called from HomePage. isDrawerOpened=$opened');
    if (opened) {
      Navigator.of(_context, rootNavigator: true).pop();
    }
    return opened;
  }

  void handleAppExit() async {
    if (drawerClosedTriggered()) {
      return;
    }

    if (!_dialogOpened) {
      _dialogOpened = true;
      await QuickAlert.show(
        context: _context,
        type: QuickAlertType.confirm,
        text: 'Do you want to close Konbit App',
        confirmBtnText: 'Yes',
        cancelBtnText: 'No',
        confirmBtnColor: Colors.green,
        showConfirmBtn: true,
        onConfirmBtnTap: () {
          SystemNavigator.pop();
        },
      );
      _dialogOpened = false;
    }

    if (_context.mounted) {
      Navigator.of(_context, rootNavigator: true).pop();
    }
  }
}
