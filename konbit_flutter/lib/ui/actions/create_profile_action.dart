import 'package:flutter/foundation.dart';
import 'package:konbit_flutter/core/api/interceptor/http_interceptor.dart';
import 'package:konbit_flutter/core/api/user_profile_api.dart';
import 'package:konbit_flutter/core/model/input/user_profile_form.dart';
import 'package:konbit_flutter/core/model/output/http_konbit_message.dart';
import 'package:konbit_flutter/ui/actions/base_action.dart';

import '../../core/api/dio_builder.dart';
import '../../core/config/konbit_config.dart';

class CreateProfileAction
    extends BaseAction<HttpKonbitMessage, UserProfileForm> {
  CreateProfileAction(
      {required super.cancelToken,
      required super.onSuccess,
      required super.onError})
      : _userProfileApi = UserProfileApi(
            DioBuilder.create()
                .baseUrl(KonbitConfig.getConfig().getUrl())
                .interceptor(AccessTokenInterceptor.getSingleInstance())
                .build(),
            cancelToken);

  final UserProfileApi _userProfileApi;

  @override
  Future submitData(UserProfileForm userProfileForm) async {
    try {
      final response = await _userProfileApi.createProfile(userProfileForm);
      final konbitMessage = response!.data!;
      debugPrint(konbitMessage.toJson().toString());
      onSuccess(konbitMessage);
    } on Exception catch (ex, stackTrace) {
      debugPrint(stackTrace.toString());
      onError(ex, userProfileForm);
    }
  }
}
