import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';

abstract class BaseAction<R, I> {
  BaseAction({required this.cancelToken,required this.onSuccess, required this.onError});

  @protected
  final CancelToken cancelToken;

  @protected
  final void Function(R result) onSuccess;
  
  @protected
  final void Function(Exception exception, I input) onError;


  Future<dynamic> submitData(I input);
}
