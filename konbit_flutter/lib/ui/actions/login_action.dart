import 'package:konbit_flutter/core/model/input/login_info.dart';
import 'package:konbit_flutter/core/model/output/jwt_token.dart';
import 'package:konbit_flutter/ui/actions/base_action.dart';

import '../../core/api/dio_builder.dart';
import '../../core/api/user_api.dart';
import '../../core/config/konbit_config.dart';

class LoginAction extends BaseAction<JwtToken, LoginInfo> {
  LoginAction(
      {required super.cancelToken,
      required super.onSuccess,
      required super.onError})
      : _userApi = UserApi(
            DioBuilder.create()
                .baseUrl(KonbitConfig.getConfig().getUrl())
                .build(),
            cancelToken);

  final UserApi _userApi;

  @override
  Future<bool> submitData(LoginInfo loginInfo) async {
    try {
      final loginResponse = await _userApi.login(loginInfo);
      final JwtToken jwtToken = loginResponse!.data!;
      onSuccess(jwtToken);
      return true;
    } on Exception catch (ex) {
      onError(ex, loginInfo);
    }
    return false;
  }
}
