import 'dart:async';

import '../../core/model/db_entity/base_place.dart';
import '../../core/model/db_entity/city.dart';
import '../../core/model/db_entity/state.dart';

FutureOr<List<P>> searchSuggestionsCallback<P extends BasePlace>(
    String search, Future<List<P>> Function(String search) lookupFunction,
    [int Function(P model1, P model2)? compare]) async {
  final city = search.trim();
  if (city.length < 2) {
    return [];
  }

  final List<P> places = await lookupFunction(search);

  if (compare != null) {
    places.sort(compare);
    return places;
  }

  places
      .sort((place1, place2) => place1.toString().compareTo(place2.toString()));
  return places;
}

int cityPlaceModelCompare(BasePlace model1, BasePlace model2) {
  final city1 = model1 as City;
  final city2 = model2 as City;

  final country1 = city1.country!;
  final country2 = city2.country!;
  final int countryCmp = country1.name.compareTo(country2.name);

  if (countryCmp != 0) {
    return countryCmp;
  }

  final state1 = city1.state!;
  final state2 = city2.state!;
  final stateCmp = state1.name.compareTo(state2.name);

  if (stateCmp != 0) {
    return stateCmp;
  }

  return city1.name.compareTo(city2.name);
}

int statePlaceModelCompare(BasePlace model1, BasePlace model2) {
  final state1 = model1 as State;
  final state2 = model2 as State;

  final country1 = state1.country!;
  final country2 = state2.country!;
  final int countryCmp = country1.name.compareTo(country2.name);

  if (countryCmp != 0) {
    return countryCmp;
  }

  return state1.name.compareTo(state2.name);
}
