import 'package:flutter/foundation.dart';
import 'package:konbit_flutter/ui/actions/base_action.dart';

import '../../core/api/dio_builder.dart';
import '../../core/api/user_api.dart';
import '../../core/config/konbit_config.dart';
import '../../core/model/input/user_subscription.dart';
import '../../core/model/output/user_dto_info.dart';

class RegistrationAction extends BaseAction<UserDtoInfo, UserSubscription> {
  final UserApi _userApi;

  RegistrationAction(
      {required super.cancelToken,
      required super.onSuccess,
      required super.onError})
      : _userApi = UserApi(
            DioBuilder.create()
                .baseUrl(KonbitConfig.getConfig().getUrl())
                .build(),
            cancelToken);

  @override
  Future<void> submitData(UserSubscription subscription) async {
    try {
      final response = await _userApi.registerUser(subscription);
      final userDto = response!.data!;
      debugPrint(userDto.toJson().toString());
      onSuccess(userDto);
    } on Exception catch (ex, stackTrace) {
      debugPrint(stackTrace.toString());
      onError(ex, subscription);
    }
  }
}
