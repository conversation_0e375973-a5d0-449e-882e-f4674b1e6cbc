import 'dart:io';

import 'package:beamer/beamer.dart';
import 'package:camera/camera.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:konbit_flutter/core/exception/app_exception.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/core/model/user_auth_data.dart';
import 'package:konbit_flutter/ui/services/profile_search_service.dart';
import 'package:konbit_flutter/ui/widgets/error_component/error_banner.dart';
import 'package:konbit_flutter/ui/widgets/progress_bar_widget.dart';
import 'package:provider/provider.dart';

import '../../core/helper/api_helper.dart';
import '../screens/take_picture_screen.dart';

class UploadPicturePage extends StatelessWidget {
  const UploadPicturePage({super.key, this.redirectPath = '/home'});

  final String redirectPath;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const TitleWidget(title: 'Upload Picture'),
          Expanded(
            child: UploadPictureForm(
              redirectPath: redirectPath,
            ),
          ),
        ],
      ),
    );
  }
}

class TitleWidget extends StatelessWidget {
  final String title;

  const TitleWidget({super.key, required this.title});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      color: Colors.blue.shade400,
      child: SafeArea(
        bottom: false,
        child: Center(
          child: Text(
            title,
            style: const TextStyle(
              fontSize: 22,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ),
      ),
    );
  }
}

class UploadPictureForm extends StatefulWidget {
  const UploadPictureForm({super.key, required String redirectPath})
      : _redirectPath = redirectPath;
  final String _redirectPath;

  @override
  State<UploadPictureForm> createState() => _UploadPictureFormState();
}

class _UploadPictureFormState extends State<UploadPictureForm> {
  File? _imageFile;
  bool _isSubmitting = false;
  String? _bannerError;
  late CancelToken _cancelToken;
  List<CameraDescription>? _cameras;

  @override
  void initState() {
    super.initState();
    _cancelToken = CancelToken();
    _initializeCamera();
  }

  Future<void> _initializeCamera() async {
    try {
      _cameras = await availableCameras();
      setState(() {});
    } catch (e) {
      setState(() {
        _bannerError = 'Failed to initialize camera: $e';
      });
    }
  }

  @override
  void dispose() {
    if (!_cancelToken.isCancelled) {
      _cancelToken.cancel('Widget disposed');
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Center(
        child: _isSubmitting ? _buildProgressWidget() : _buildFormContent(),
      ),
    );
  }

  Widget _buildProgressWidget() {
    return ProgressBarWidget(
      label: 'Uploading picture...',
      onCancel: () {
        _cancelToken.cancel('Cancelling picture upload');
        setState(() {
          _isSubmitting = false;
        });
      },
    );
  }

  Widget _buildFormContent() {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          _buildErrorBanner(),
          _buildImagePreview(),
          const SizedBox(height: 20),
          //_buildImageSourceButtons(),
          const SizedBox(height: 30),
          if (_imageFile != null) _buildUploadButton(),
          //const SizedBox(height: 30),
          _skipUploadButton(),
        ],
      ),
    );
  }

  Widget _buildErrorBanner() {
    return _bannerError != null
        ? ErrorBannerWidget(
            text: _bannerError!,
            onDismiss: () {
              setState(() {
                _bannerError = null;
              });
            },
          )
        : const SizedBox.shrink();
  }

  Widget _buildImagePreview() {
    return Container(
      width: double.infinity,
      height: 300,
      decoration: BoxDecoration(
        color: Colors.grey.shade200,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade400),
      ),
      child: _imageFile != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.file(
                _imageFile!,
                fit: BoxFit.cover,
              ),
            )
          : const Center(
              child: Text(
                'No image selected',
                style: TextStyle(color: Colors.grey, fontSize: 16),
              ),
            ),
    );
  }

  Widget _buildImageSourceButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        ElevatedButton.icon(
          onPressed: _pickImageFromGallery,
          icon: const Icon(Icons.photo_library),
          label: const Text('Gallery'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.blue.shade300,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          ),
        ),
        ElevatedButton.icon(
          onPressed: _cameras != null ? _takePicture : null,
          icon: const Icon(Icons.camera_alt),
          label: const Text('Camera'),
          style: ElevatedButton.styleFrom(
            backgroundColor: Colors.green.shade400,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
          ),
        ),
      ],
    );
  }

  Widget _buildUploadButton() {
    return ElevatedButton(
      onPressed: _uploadImage,
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.blue.shade600,
        foregroundColor: Colors.white,
        minimumSize: const Size(double.infinity, 50),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: const Text(
        'Upload Picture',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Widget _skipUploadButton() {
    return ElevatedButton(
      onPressed: () {
        if (context.mounted) {
          logger.info('Skipping picture upload');
          context.beamToNamed(widget._redirectPath);
        }
      },
      style: ElevatedButton.styleFrom(
        backgroundColor: Colors.red.shade600,
        foregroundColor: Colors.white,
        minimumSize: const Size(double.infinity, 50),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: const Text(
        'Skip Picture Upload',
        style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
      ),
    );
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final pickedFile = await ImagePicker().pickImage(
        source: ImageSource.gallery,
        maxWidth: 1800,
        maxHeight: 1800,
      );

      if (pickedFile != null) {
        setState(() {
          _imageFile = File(pickedFile.path);
        });
      }
    } catch (e) {
      setState(() {
        _bannerError = 'Failed to pick image: $e';
      });
    }
  }

  Future<void> _takePicture() async {
    if (_cameras == null || _cameras!.isEmpty) {
      setState(() {
        _bannerError = 'No camera available';
      });
      return;
    }

    await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TakePictureScreen(
          onPictureTaken: (File imageFile) {
            setState(() {
              _imageFile = imageFile;
            });
          },
        ),
      ),
    );
  }

  Future<void> _uploadImage() async {
    if (_imageFile == null) {
      setState(() {
        _bannerError = 'Please select an image first';
      });
      return;
    }

    setState(() {
      _isSubmitting = true;
      _bannerError = null;
    });

    if (_cancelToken.isCancelled) {
      _cancelToken = CancelToken();
    }

    final authData = Provider.of<UserAuthData>(context, listen: false);

    if (!await _updateUserAuthData(authData)) return;
    if (!await _uploadProfilePicture()) return;
    if (!await _updateUserProfileData(authData)) return;

    if (mounted) {
      setState(() {
        _isSubmitting = false;
      });
      context.beamToNamed(widget._redirectPath);
    }
  }

  Future<bool> _updateUserAuthData(UserAuthData authData) async {
    try {
      final profileSearchService =
          ProfileSearchService(cancelToken: _cancelToken);
      final userData = await profileSearchService.fetchOrUpdateUserAuthData(
        authData: authData,
        forceTokenRefresh: true,
        onTokenMissing: () {
          authData.updateUserData(UserAuthData());
          context.beamToNamed('/');
        },
        onProfileMissing: (token) => context.beamToNamed('/create_profile'),
      );
      authData.updateUserData(userData);
      return true;
    } on Exception catch (ex, stackTrace) {
      _handleError(ex, stackTrace, 'Failed to update user auth data');
      return false;
    }
  }

  Future<bool> _uploadProfilePicture() async {
    try {
      final profilePictureApi =
          ProfilePictureApiBuilder.withAuthentication(_cancelToken).build();
      final response =
          await profilePictureApi.uploadProfilePicture(_imageFile!);
      if (response != null) {
        final konbitMessage = response.data!;
        logger.info(konbitMessage.toJson().toString());
      }
      return true;
    } on Exception catch (ex, stackTrace) {
      _handleError(ex, stackTrace, 'Failed to upload picture');
      return false;
    }
  }

  Future<bool> _updateUserProfileData(UserAuthData authData) async {
    try {
      final userProfileApi =
          UserProfileApiBuilder.withAuthentication(_cancelToken).build();
      final response = await userProfileApi.getMyProfile();
      if (response != null) {
        final userProfile = response.data!;
        authData.userProfile = userProfile;
      }
      return true;
    } on Exception catch (ex, stackTrace) {
      _handleError(
          ex, stackTrace, 'Failed to fetch user profile after picture upload');
      return false;
    }
  }

  void _handleError(Exception ex, StackTrace stackTrace, String context) {
    String errorMessage = _getErrorMessage(ex);
    logger.warning('$context with error: $ex');
    logger.warning('Stacktrace: $stackTrace');
    setState(() {
      _bannerError = errorMessage;
      _isSubmitting = false;
    });
  }

  String _getErrorMessage(Object ex) {
    return ex is AppException
        ? ex.getHumanReadableError('Failed to upload picture: ${ex.message}')
        : 'Failed to upload picture: ${ex.toString()}';
  }
}
