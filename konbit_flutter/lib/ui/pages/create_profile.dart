import 'package:beamer/beamer.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/database/world_info_dao.dart';
import 'package:konbit_flutter/core/helper/api_helper.dart';
import 'package:konbit_flutter/core/helper/token_helper.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/core/model/app_metadata.dart';
import 'package:konbit_flutter/core/model/db_entity/base_place.dart';
import 'package:konbit_flutter/core/model/db_entity/country.dart';
import 'package:konbit_flutter/core/model/input/user_profile_form.dart';
import 'package:konbit_flutter/core/model/output/user_profile_dto.dart';
import 'package:konbit_flutter/ui/actions/create_profile_action.dart';
import 'package:konbit_flutter/ui/pages/create_profile/profile_reactive_form.dart';
import 'package:provider/provider.dart';
import 'package:reactive_date_time_picker/reactive_date_time_picker.dart';
import 'package:reactive_dropdown_search/reactive_dropdown_search.dart';
import 'package:reactive_forms/reactive_forms.dart';
import 'package:reactive_phone_form_field/reactive_phone_form_field.dart';
import 'package:toastification/toastification.dart';

import '../../core/exception/app_exception.dart';
import '../../core/model/db_entity/city.dart';
import '../../core/model/db_entity/state.dart' as geo_place;
import '../../core/model/output/http_konbit_message.dart';
import '../../core/model/user_auth_data.dart';
import '../../core/model/user_info.dart';
import '../actions/search_suggestions.dart';
import '../widgets/error_component/error_banner.dart';
import '../widgets/form_component/reactive_flutter_type_ahead.dart';
import '../widgets/form_component/text_tag_widget.dart';
import '../widgets/form_component/textfield_component.dart';
import '../widgets/progress_bar_widget.dart';

class CreateProfilePage extends StatefulWidget {
  const CreateProfilePage({super.key});

  @override
  State<CreateProfilePage> createState() => _CreateProfilePageState();
}

class _CreateProfilePageState extends State<CreateProfilePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(child: _CreateProfileForm()),
    );
  }
}

class _CreateProfileForm extends StatefulWidget {
  const _CreateProfileForm();

  @override
  State<_CreateProfileForm> createState() => _CreateProfileFormState();
}

class _CreateProfileFormState extends State<_CreateProfileForm> {
  late final ProfileReactiveForm _profileReactiveForm =
      ProfileReactiveForm.create();

  late WorldInfoDao _worldInfoDao;
  late CancelToken _cancelToken;

  bool _isSubmitting = false;
  String? _bannerError;
  late String _username;

  @override
  void initState() {
    super.initState();
    final appMetaData = Provider.of<AppMetadata>(context, listen: false);
    _worldInfoDao = WorldInfoDao(database: appMetaData.geoDatabase);
    final authData = Provider.of<UserAuthData>(context, listen: false);
    _username =
        authData.token != null ? getUsernameFromToken(authData.token!) : '';

    // Initialize form progress tracking
    //_updateFormProgress();
  }

  void _handleRegistrationSuccess(HttpKonbitMessage konbitMessage) async {
    setState(() {
      _isSubmitting = false;
    });
    final userInfo = Provider.of<UserInfo>(context, listen: false);
    logger.info("User profile for $_username is created successfully");
    logger.info(konbitMessage.toJson().toString());
    final userProfileApi =
        UserProfileApiBuilder.withAuthentication(CancelToken()).build();
    UserProfileDto userProfile = UserProfileDto()
      ..username = userInfo.username
      ..visible = true;

    try {
      final response = await userProfileApi.getMyProfile();
      if (response != null) {
        userProfile = response.data ?? userProfile;
      }
    } finally {
      if (context.mounted) {
        final UserAuthData authData =
            Provider.of<UserAuthData>(context, listen: false);
        authData.userProfile = userProfile;
        context.beamToNamed("/home/<USER>");
      }
    }
  }

  void _handleRegistrationError(Exception exception, UserProfileForm form) {
    final String errMsg = _getErrorMessage(exception);

    // Show only toast notification for better UX
    toastification.show(
      context: context,
      description: RichText(text: TextSpan(text: errMsg)),
      autoCloseDuration: const Duration(seconds: 5),
      backgroundColor: Colors.deepOrange,
      alignment: AlignmentDirectional.bottomCenter,
      type: ToastificationType.error,
    );

    setState(() {
      _isSubmitting = false;
    });
  }

  String _getErrorMessage(Exception exception) {
    const String defaultErr = "Unexpected Error. Please Retry later";

    if (exception is! AppException) return defaultErr;

    if (exception.httpMessage == null) {
      return exception.getHumanReadableError(defaultErr);
    }

    switch (exception.httpMessage!.status) {
      case KonbitMessageType.DuplicateProfile:
        return "Sorry! Profile already exists for user '$_username'";
      case KonbitMessageType.NotFound:
        return "No user with username '$_username' is found to create the profile";
      default:
        return defaultErr;
    }
  }

  Future<void> _submitProfile() async {
    await CreateProfileAction(
            cancelToken: _cancelToken,
            onSuccess: _handleRegistrationSuccess,
            onError: _handleRegistrationError)
        .submitData(_profileReactiveForm.getUserProfileForm());
  }

  @override
  Widget build(BuildContext context) {
    return ReactiveForm(
      formGroup: _profileReactiveForm.form,
      //onChanged: (_) => _updateFormProgress(),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Center(
          child: _isSubmitting ? _buildProgressWidget() : _buildFormContent(),
        ),
      ),
    );
  }

  Widget _buildProgressWidget() {
    return ProgressBarWidget(
      label: 'Creating Profile for $_username ...',
      onCancel: () {
        _cancelToken.cancel('Cancelling waiting for profile creation');
        setState(() {
          _isSubmitting = false;
        });
      },
    );
  }

  Widget _buildFormContent() {
    return CustomScrollView(
      physics: const BouncingScrollPhysics(),
      slivers: [
        _buildHeader(),
        SliverToBoxAdapter(
          child: Column(
            children: [
              _buildErrorBanner(),
              _buildPersonalInfoSection(),
              _buildProfessionalInfoSection(),
              _buildAddressSection(),
              _buildContactSection(),
              _buildSubmitButton(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return PinnedHeaderSliver(
      child: Container(
        color: Theme.of(context).primaryColorLight,
        height: 50.0,
        child: Center(
          child: Text(
            'Create User Profile for $_username',
            style: Theme.of(context).textTheme.titleLarge,
          ),
        ),
      ),
    );
  }

  Widget _buildErrorBanner() {
    if (_bannerError == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.only(bottom: 8.0),
      child: ErrorBannerWidget(
        text: _bannerError!,
        onDismiss: () {
          setState(() {
            _bannerError = null;
          });
        },
      ),
    );
  }

  Widget _buildPersonalInfoSection() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Personal Information',
                style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16.0),
            const TextFieldWidget(
                formControlName: 'first_name', labelText: 'First Name'),
            const TextFieldWidget(
                formControlName: 'middle_name', labelText: 'Middle Name'),
            const TextFieldWidget(
                formControlName: 'last_name', labelText: 'Last Name'),
            const SizedBox(height: 16.0),
            ReactiveDateTimePicker(
              formControlName: 'birth_day',
              decoration: const InputDecoration(
                labelText: 'Date of Birth',
                border: OutlineInputBorder(),
                helperText: '',
                suffixIcon: Icon(Icons.calendar_month_sharp),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProfessionalInfoSection() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Professional Information',
                style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16.0),
            const TextFieldWidget(
                formControlName: 'education', labelText: 'Education'),
            const TextFieldWidget(
                formControlName: 'main_occupation',
                labelText: 'Main Occupation'),
            const TextFieldWidget(
                formControlName: 'other_occupation',
                labelText: 'Other Occupation'),
            TextTagWidget(
              formControlName: 'skills',
              labelText: 'Skills',
              hintText: 'Input one skill and press the Enter key',
              onTagAdded: (skill) => _profileReactiveForm.addSkillTag(skill),
              onTagDeleted: (skill) =>
                  _profileReactiveForm.removeSkillTag(skill),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAddressSection() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Address Information',
                style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16.0),
            const TextFieldWidget(
                formControlName: 'street_address', labelText: 'Street Address'),
            _buildCityField(),
            _buildStateField(),
            _buildCountryField(),
            const TextFieldWidget(
                formControlName: 'zip_code', labelText: 'Zip Code'),
          ],
        ),
      ),
    );
  }

  Widget _buildContactSection() {
    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Contact & Additional Information',
                style: Theme.of(context).textTheme.titleMedium),
            const SizedBox(height: 16.0),
            ReactivePhoneFormField<PhoneNumber>(
              decoration: const InputDecoration(labelText: 'Phone'),
              formControlName: 'phone',
              focusNode: FocusNode(),
            ),
            const SizedBox(height: 16.0),
            const TextFieldWidget(
              formControlName: 'bio',
              labelText: 'Description',
              maxLines: 5,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCityField() {
    return ReactiveTypeAhead<City>(
      formControlName: 'city',
      valueAccessor: PlaceValueAccessor(),
      decoration:
          const InputDecoration(labelText: 'City', hintText: 'typing city...'),
      hideOnEmpty: true,
      suggestionsCallback: (search) {
        return searchSuggestionsCallback(
          search,
          (search) => _worldInfoDao.cityLookup(search),
          (model1, model2) => cityPlaceModelCompare(model1, model2),
        );
      },
      itemBuilder: (context, city) {
        return ListTile(
          leading: CircleAvatar(child: Text(city.country!.flag)),
          title: Text(city.name),
          trailing: Text('${city.country!.name}\n${city.state!.name}'),
        );
      },
      onSelected: (city) {
        _profileReactiveForm.setValue('state', city.state);
        _profileReactiveForm.setValue('country', city.country);
      },
    );
  }

  Widget _buildStateField() {
    return ReactiveTypeAhead<geo_place.State>(
      formControlName: 'state',
      valueAccessor: PlaceValueAccessor(),
      decoration: const InputDecoration(
          labelText: 'State', hintText: 'typing state...'),
      hideOnEmpty: true,
      suggestionsCallback: (search) => searchSuggestionsCallback(
        search,
        (search) => _worldInfoDao.stateLookup(search),
        (model1, model2) => statePlaceModelCompare(model1, model2),
      ),
      itemBuilder: (context, geoState) {
        return ListTile(
          leading: CircleAvatar(child: Text(geoState.country!.flag)),
          title: Text(geoState.name),
          trailing: Text(geoState.country!.name),
        );
      },
      onSelected: (geoState) {
        _profileReactiveForm.setValue('country', geoState.country);
      },
    );
  }

  Widget _buildCountryField() {
    return ReactiveDropdownSearch<Country, Country>(
      formControlName: 'country',
      dropdownDecoratorProps: const DropDownDecoratorProps(
          decoration: InputDecoration(
        labelText: 'Country',
        hintText: 'Select a country',
      )),
      compareFn: (item1, item2) => item1.iso3 == item2.iso3,
      items: (search, _) {
        return search.trim().isEmpty
            ? _worldInfoDao.findAllCountries()
            : _worldInfoDao.countryLookup(search.trim());
      },
      dropdownBuilder: (context, selectedCountry) {
        if (selectedCountry == null) {
          return const SizedBox.shrink();
        }

        return ListTile(
          contentPadding: const EdgeInsets.only(left: 0),
          leading: CircleAvatar(child: Text(selectedCountry.flag)),
          title: Text(selectedCountry.name),
        );
      },
      popupProps: PopupProps.menu(
        disableFilter: true,
        showSearchBox: true,
        showSelectedItems: true,
        itemBuilder: (ctx, country, isDisabled, isSelected) {
          return ListTile(
            leading: CircleAvatar(child: Text(country.flag)),
            selected: isSelected,
            title: Text(country.name),
          );
        },
      ),
    );
  }

  Widget _buildSubmitButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 24.0),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: Colors.green,
          minimumSize: const Size(double.infinity, 48.0),
          elevation: 8.0,
        ),
        onPressed: _createProfile,
        child: Text(
          'Create Profile',
          style: const TextStyle(
              fontWeight: FontWeight.w600, color: Colors.white, fontSize: 20.0),
        ),
      ),
    );
  }

  Future<void> _createProfile() async {
    if (_profileReactiveForm.isInvalid()) {
      _profileReactiveForm.markAllAsTouched();
      logger.error('Form is invalid');
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    _cancelToken = CancelToken();
    try {
      await processAndRetrieveToken(cancelToken: _cancelToken);
    } on Exception catch (ex, _) {
      logger.warning('Failed processing token with error :  $ex');
      _bannerError = 'Failed processing token with error :  $ex';
      _isSubmitting = false;
      _cancelToken.cancel('Cancel Token for token retrieval error');
    }

    await _submitProfile();
  }
}

class PlaceValueAccessor<T extends BasePlace>
    extends ControlValueAccessor<T, String> {
  @override
  String? modelToViewValue(T? modelValue) {
    if (modelValue == null) {
      return '';
    }
    return modelValue.toString();
  }

  @override
  T? viewToModelValue(String? viewValue) {
    if (viewValue == null) {
      if (T == City) {
        return City(name: '') as T;
      }

      if (T == geo_place.State) {
        return geo_place.State(name: '') as T;
      }

      throw UnimplementedError(
          'Value accessor is not implemented for $runtimeType');
    }

    if (T == City) {
      return City(name: viewValue) as T;
    }

    if (T == geo_place.State) {
      return geo_place.State(name: viewValue) as T;
    }
    throw UnimplementedError(
        'Value accessor is not implemented for $runtimeType');
  }
}
