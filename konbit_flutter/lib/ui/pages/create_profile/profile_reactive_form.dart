import 'package:reactive_forms/reactive_forms.dart';
import 'package:reactive_phone_form_field/reactive_phone_form_field.dart';

import '../../../core/model/db_entity/city.dart';
import '../../../core/model/db_entity/country.dart';
import '../../../core/model/db_entity/state.dart' as geo_place;
import '../../../core/model/input/user_profile_form.dart';

class ProfileReactiveForm {
  final FormGroup _form;
  final List<String> _skillTags = List.empty(growable: true);
  final _userProfileForm = UserProfileForm();

  factory ProfileReactiveForm.create() => ProfileReactiveForm();

  ProfileReactiveForm()
      : _form = FormGroup({
          'first_name': FormControl<String>(validators: [
            Validators.required,
            Validators.minLength(2),
            Validators.maxLength(50),
          ]),
          'last_name': FormControl<String>(validators: [
            Validators.required,
            Validators.minLength(2),
            Validators.maxLength(50),
          ]),
          'middle_name':
              FormControl<String>(validators: [Validators.maxLength(50)]),
          'birth_day': FormControl<DateTime>(
              validators: [Validators.delegate(_validateAge)]),
          'nationality': FormControl<String>(),
          'education': FormControl<String>(),
          'main_occupation': FormControl<String>(validators: [
            Validators.required,
          ]),
          'other_occupation': FormControl<String>(),
          'skills': FormControl<String>(
              validators: [Validators.delegate(_validateTagInput)]),
          'street_address': FormControl<String>(),
          'city': FormControl<City>(validators: [
            Validators.required,
          ]),
          'state': FormControl<geo_place.State>(validators: [
            Validators.required,
          ]),
          'country': FormControl<Country>(validators: [
            Validators.required,
          ]),
          'zip_code': FormControl<String>(),
          'phone':
              FormControl<PhoneNumber>(validators: [PhoneValidators.valid]),
          'bio': FormControl<String>(validators: [Validators.maxLength(500)]),
        });

  FormGroup get form => _form;

  bool isInvalid() => _form.invalid;

  void markAllAsTouched() => _form.markAllAsTouched();

  dynamic getValue(String controlName) => _form.control(controlName).value;

  void setValue(String controlName, dynamic value) {
    _form.control(controlName).value = value;
  }

  void addSkillTag(String tag) {
    _skillTags.add(tag);
  }

  void removeSkillTag(String tag) {
    _skillTags.remove(tag);
  }

  UserProfileForm getUserProfileForm() {
    _userProfileForm.firstname = getValue('first_name') as String?;
    _userProfileForm.middlename = getValue('middle_name') as String?;
    _userProfileForm.lastname = getValue('last_name') as String?;
    if (getValue('birth_day') != null) {
      final anniversary = getValue('birth_day') as DateTime;
      _userProfileForm.birthDate = anniversary.day;
      _userProfileForm.birthMonth = anniversary.month;
      _userProfileForm.birthYear = anniversary.year;
    }
    _userProfileForm.education = getValue('education') as String?;
    _userProfileForm.mainOccupation = getValue('main_occupation') as String?;
    _userProfileForm.secondOccupation = getValue('other_occupation') as String?;

    _userProfileForm.skills = _skillTags;

    _userProfileForm.streetAddress = getValue('street_address') as String?;
    if (getValue('city') != null) {
      final city = getValue('city') as City;
      _userProfileForm.city = city.name;
    }

    if (getValue('state') != null) {
      final state = getValue('state') as geo_place.State;
      _userProfileForm.locality1 = state.name;
    }

    final country = getValue('country') as Country;
    _userProfileForm.country = country.iso3;
    _userProfileForm.zipCode = getValue('zip_code') as String?;

    if (getValue('phone') != null) {
      final phoneNumber = getValue('phone') as PhoneNumber;
      _userProfileForm.phone =
          '+${phoneNumber.countryCode}-${phoneNumber.formatNsn()}';
    }

    _userProfileForm.bio = getValue('bio') as String?;
    return _userProfileForm;
  }
}

Map<String, dynamic>? _validateAge(AbstractControl<dynamic> control) {
  if (control.value != null) {
    final birthDate = control.value as DateTime;
    final age = DateTime.now().difference(birthDate).inDays ~/ 365;
    if (age < 13) {
      return {'minimumAge': true};
    }
  }
  return null;
}

/// Custom function that validates that control's value must be `true`.
Map<String, dynamic>? _validateTagInput(AbstractControl<dynamic> control) {
  if (control.isNotNull &&
      control.value is String &&
      control.value != null &&
      control.value.toString().trim().isNotEmpty) {
    String tag = control.value.toString().trim();
    return {"Press enter to add '$tag' to Skills list": true};
  }

  return null;
}
