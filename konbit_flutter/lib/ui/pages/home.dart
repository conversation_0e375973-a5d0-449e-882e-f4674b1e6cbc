import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/ui/actions/app_exit_handler.dart';
import 'package:konbit_flutter/ui/screens/home_screen.dart';
import 'package:sheet/sheet.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _SlidingWindowState {
  bool opened = false;
}

class _HomePageState extends State<HomePage> {
  final _slidingWindowState = _SlidingWindowState();
  final _sheetController = SheetController();
  bool dialogOpened = false;
  late AppExitHandler _appExitHandler;

  @override
  void initState() {
    super.initState();
    _appExitHandler = AppExitHandler(context);
    _sheetController.addListener(
      () {
        //logger.info('Sliding position : ${widget._sheetController.position}');
        if (!_slidingWindowState.opened) {
          _slidingWindowState.opened = true;
        }
      },
    );
  }

  Future<bool> _hasSheetSlided() async {
    if (_slidingWindowState.opened) {
      _sheetController.animateTo(30.0,
          duration: const Duration(milliseconds: 100),
          curve: Curves.easeInOutCubic);
      await Future.delayed(Duration(milliseconds: 400));
      _slidingWindowState.opened = false;
      return true;
    }
    return false;
  }

  @override
  Widget build(BuildContext context) {
    logger.debug('HomeWidget Build fired!!!!');

    return PopScope(
      canPop: false,
      child: Scaffold(
        body: SafeArea(
          child: HomeScreen(sheetController: _sheetController),
        ),
      ),
      onPopInvokedWithResult: (didPop, result) async {
        if (_appExitHandler.drawerClosedTriggered()) {
          return;
        }

        final bool slided = await _hasSheetSlided();
        logger.info('PopeScope is called from HomePage. SheetSlided=$slided');
        if (!slided) {
          _appExitHandler.handleAppExit();
        }
      },
    );
  }
}
