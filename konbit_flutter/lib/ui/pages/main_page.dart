import 'dart:async';

import 'package:beamer/beamer.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/helper/token_helper.dart';
import 'package:konbit_flutter/core/logger/logger.dart';
import 'package:konbit_flutter/core/model/drawer_info.dart';
import 'package:konbit_flutter/ui/beamer_navigation/home_location.dart';
import 'package:konbit_flutter/ui/widgets/drawer_widget.dart';
import 'package:provider/provider.dart';

import '../../core/model/user_auth_data.dart';
import '../widgets/app_bar_widget.dart';

class MainPage extends StatefulWidget {
  const MainPage({super.key});

  @override
  State<MainPage> createState() => _MainPageState();
}

class _MainPageState extends State<MainPage> {
  final GlobalKey<ScaffoldState> _key = GlobalKey<ScaffoldState>();
  final _beamerKey = GlobalKey<BeamerState>();

  late Timer periodicTimer;

  @override
  void initState() {
    super.initState();
    periodicTimer = Zone.current.createPeriodicTimer(
      Duration(minutes: 3),
      (timer) async {
        logger.info(
            'Periodic timer - refresh user token session. Context isMounted=${context.mounted}');
        try {
          final UserAuthData userAuthData = await processAndRetrieveToken(
            cancelToken: CancelToken(),
            onValidToken: (token) =>
                logger.info('Token refreshed successfully by Periodic Timer'),
          );
          if (context.mounted) {
            Provider.of<UserAuthData>(context, listen: false)
                .updateUserData(userAuthData);
          }
        } catch (ex) {
          logger.error('Timer failed to refresh token : $ex}');
        }
      },
    );
  }

  BeamerDelegate _createRouterDelegate() => BeamerDelegate(
        locationBuilder: BeamerLocationBuilder(beamLocations: [
          HomeLocation(),
        ]).call,
        guards: <BeamGuard>[
          BeamGuard(
            pathPatterns: ['/home'],
            check: (context, location) =>
                Provider.of<UserAuthData>(context, listen: false)
                    .isAuthenticated(),
            beamToNamed: (origin, target) => '/',
          ),
        ],
      );

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      key: _key,
      appBar: AppBar(
        title: AppBarWidget(),
        elevation: 5.0,
      ),
      body: Beamer(key: _beamerKey, routerDelegate: _createRouterDelegate()),
      drawer: AppDrawer(),
      onDrawerChanged: (isOpened) {
        Provider.of<DrawerInfo>(context, listen: false).isOpened = isOpened;
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
    logger.info('Cancelling periodic timer');
    periodicTimer.cancel();
  }
}
