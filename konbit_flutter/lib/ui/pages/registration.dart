import 'dart:async';

import 'package:beamer/beamer.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/exception/app_exception.dart';
import 'package:konbit_flutter/core/model/input/user_subscription.dart';
import 'package:konbit_flutter/core/model/output/http_konbit_message.dart';
import 'package:konbit_flutter/core/model/output/user_dto_info.dart';
import 'package:konbit_flutter/ui/widgets/app_bar_widget.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:reactive_forms/reactive_forms.dart';
import 'package:toastification/toastification.dart';

import '../actions/registration_action.dart';
import '../custom_validators/validator_message/validator_message.dart';
import '../widgets/error_component/error_banner.dart';
import '../widgets/form_component/textfield_component.dart';

class RegistrationPage extends StatelessWidget {
  const RegistrationPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.blue.shade200,
          title: const AppBarWidget(title: 'Create Account'),
        ),
        body: const RegistrationForm(),
      ),
    );
  }
}

class RegistrationForm extends StatefulWidget {
  const RegistrationForm({super.key});

  @override
  State<RegistrationForm> createState() => _RegistrationFormState();
}

class _RegistrationFormState extends State<RegistrationForm> {
  String? _bannerError;
  bool _isSubmitting = false;
  CancelToken _cancelToken = CancelToken();

  final _form = FormGroup({
    'username': FormControl<String>(
        validators: [Validators.required, Validators.minLength(3)]),
    'email': FormControl<String>(
        validators: [Validators.required, Validators.email]),
    'emailConfirmation': FormControl<String>(validators: [
      Validators.required,
      Validators.email,
    ]),
    'password': FormControl<String>(validators: [
      Validators.required,
      Validators.minLength(7),
    ]),
    'passwordConfirmation': FormControl<String>(validators: [
      Validators.required,
    ]),
  }, validators: [
    Validators.mustMatch('email', 'emailConfirmation'),
    Validators.mustMatch('password', 'passwordConfirmation'),
  ]);

  @override
  Widget build(BuildContext context) {
    return ReactiveForm(
        formGroup: _form,
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: SingleChildScrollView(
            child: Column(
              children: [
                _buildErrorBanner(),
                _buildFormFields(),
                const SizedBox(height: 28.0),
                _buildSubmitButton(),
              ],
            ),
          ),
        ));
  }

  Future<void> _registerUser() async {
    if (!_form.valid) {
      _form.markAllAsTouched();
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    final subscription = _createSubscription();

    if (_cancelToken.isCancelled) {
      _cancelToken = CancelToken();
    }

    _showLoadingDialog();

    await RegistrationAction(
      onSuccess: _onRegistrationSuccess,
      onError: _handleRegistrationError,
      cancelToken: _cancelToken,
    ).submitData(subscription);
  }

  UserSubscription _createSubscription() {
    final username = _form.control('username').value as String;
    final email = _form.control('email').value as String;
    final password = _form.control('password').value as String;

    return UserSubscription(
        username: username, email: email, password: password);
  }

  void _showLoadingDialog() {
    QuickAlert.show(
      context: context,
      type: QuickAlertType.loading,
      barrierDismissible: true,
      showCancelBtn: true,
      disableBackBtn: true,
      title: 'Account Creation',
      text: 'Waiting for account creation to complete',
      onCancelBtnTap: () => _cancelToken.cancel(),
    );
  }

  void _onRegistrationSuccess(UserDtoInfo userInfo) async {
    Navigator.of(context, rootNavigator: true).pop();

    await QuickAlert.show(
      context: context,
      title: 'Account Creation Success',
      type: QuickAlertType.success,
      text: "Account creation for '${userInfo.username}' is successful."
          " Please check your email to activate your account before you can login to the app",
      onConfirmBtnTap: () {
        context.beamToNamed('/');
      },
    );
  }

  void _handleRegistrationError(
      Exception exception, UserSubscription userSubscription) {
    Navigator.of(context, rootNavigator: true).pop();

    final errMsg = _getErrorMessage(exception, userSubscription);

    _showErrorToast(errMsg);

    setState(() {
      _bannerError = errMsg;
      _isSubmitting = false;
    });
  }

  String _getErrorMessage(
      Exception exception, UserSubscription userSubscription) {
    String defaultErr = "Unexpected Error. Please Retry later";

    if (exception is! AppException) return defaultErr;

    final appException = exception;
    if (appException.httpMessage == null) {
      return appException.getHumanReadableError(defaultErr);
    }

    switch (appException.httpMessage!.status) {
      case KonbitMessageType.UsernameAlreadyExist:
        return "Sorry! Username '${userSubscription.username}' is already in use. Please choose another one";
      case KonbitMessageType.EmailAlreadyExist:
        return "Email '${userSubscription.email}' is already in use. Choose another email";
      default:
        return defaultErr;
    }
  }

  void _showErrorToast(String message) {
    toastification.show(
      context: context,
      description: RichText(text: TextSpan(text: message)),
      autoCloseDuration: const Duration(seconds: 5),
      backgroundColor: Colors.deepOrange,
      alignment: AlignmentDirectional.bottomCenter,
      type: ToastificationType.error,
    );
  }

  Widget _buildErrorBanner() {
    if (_bannerError == null) return const SizedBox(height: 10.0);

    return Column(
      children: [
        ErrorBannerWidget(
          text: _bannerError!,
          onDismiss: () {
            setState(() {
              _bannerError = null;
            });
          },
        ),
        const SizedBox(height: 10.0),
      ],
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        TextFieldWidget(
          formControlName: 'username',
          labelText: 'Username',
          validationMessages: {
            ValidationMessage.required: (error) => 'Username must not be empty',
            ValidationMessage.minLength: (error) =>
                'Username must have at least 3 characters',
          },
        ),
        TextFieldWidget(
          formControlName: 'email',
          labelText: 'Email',
          validationMessages: customValidationMessage(fieldName: 'Email'),
        ),
        TextFieldWidget(
          formControlName: 'emailConfirmation',
          labelText: 'Email Confirmation',
          validationMessages: customValidationMessage(fieldName: 'Email'),
        ),
        TextFieldWidget(
          formControlName: 'password',
          labelText: 'Password',
          obscureText: true,
          validationMessages: customValidationMessage(
              errorMessage: 'Password must be at least 7 characters long'),
        ),
        TextFieldWidget(
          formControlName: 'passwordConfirmation',
          labelText: 'Password Confirmation',
          obscureText: true,
          validationMessages:
              customValidationMessage(fieldName: 'Password Confirmation'),
        ),
      ],
    );
  }

  Widget _buildSubmitButton() {
    const buttonTxtStyle = TextStyle(
        fontWeight: FontWeight.w600, color: Colors.white, fontSize: 20.0);

    final buttonStyle = ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        minimumSize: const Size(double.infinity, 48.0),
        elevation: 10.0);

    return !_isSubmitting
        ? ElevatedButton(
            style: buttonStyle,
            onPressed: _registerUser,
            child: const Text('Create Account', style: buttonTxtStyle),
          )
        : ElevatedButton(
            style: buttonStyle,
            onPressed: () {
              _cancelToken.cancel();
              setState(() {
                _isSubmitting = false;
              });
            },
            child: const Stack(
              children: [
                Text(
                  'Cancel',
                  style: buttonTxtStyle,
                ),
                Positioned(
                    left: 20,
                    child: CircularProgressIndicator(
                      strokeAlign: 0.5,
                    )),
              ],
            ));
  }

  @override
  void dispose() {
    if (_isSubmitting && !_cancelToken.isCancelled) {
      _cancelToken.cancel();
    }
    super.dispose();
  }
}
