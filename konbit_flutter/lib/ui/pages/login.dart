import 'package:beamer/beamer.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/database/db_helper.dart';
import 'package:konbit_flutter/core/database/jwt_token_dao.dart';
import 'package:konbit_flutter/core/exception/app_exception.dart';
import 'package:konbit_flutter/core/helper/token_helper.dart';
import 'package:konbit_flutter/core/model/input/login_info.dart';
import 'package:konbit_flutter/core/model/user_auth_data.dart';
import 'package:konbit_flutter/ui/actions/login_action.dart';
import 'package:konbit_flutter/ui/widgets/app_bar_widget.dart';
import 'package:provider/provider.dart';
import 'package:reactive_forms/reactive_forms.dart';

import '../../core/model/db_entity/jwt_token_entity_builder.dart';
import '../widgets/error_component/error_banner.dart';
import '../widgets/form_component/textfield_component.dart';
import '../widgets/progress_bar_widget.dart';

class LoginPage extends StatelessWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.blue.shade400,
          title: const AppBarWidget(
            title: 'Login',
          ),
        ),
        body: const LoginFormWidget(),
      ),
    );
  }
}

class LoginFormWidget extends StatefulWidget {
  const LoginFormWidget({super.key});

  @override
  State<LoginFormWidget> createState() => _LoginFormWidgetState();
}

class _LoginFormWidgetState extends State<LoginFormWidget> {
  late CancelToken _cancelToken = CancelToken();
  bool _isSubmitting = false;
  String? _bannerError;

  final _form = FormGroup({
    'username': FormControl<String>(
        validators: [Validators.required, Validators.minLength(3)]),
    'password': FormControl<String>(
        validators: [Validators.required, Validators.minLength(7)]),
  });

  @override
  Widget build(BuildContext context) {
    const buttonTxtStyle = TextStyle(
        fontWeight: FontWeight.w600, color: Colors.white, fontSize: 20.0);

    final buttonStyle = ElevatedButton.styleFrom(
        backgroundColor: Colors.blue,
        minimumSize: const Size(double.infinity, 48.0),
        elevation: 5.0);

    return _isSubmitting
        ? Center(child: _buildProgressWidget())
        : _buildLoginForm(context, buttonStyle, buttonTxtStyle);
  }

  String _onLoginFailure(AppException ex, LoginInfo loginInfo) {
    debugPrint(
        "Login Failed for User ${loginInfo.toJson()} : ${ex.toString()}");
    String defaultError =
        "Konbit Service seems down. Please, wait before retry";
    return ex.getHumanReadableError(defaultError);
  }

  Future<void> _handleUserLogin() async {
    if (_form.invalid) {
      _form.markAllAsTouched();
      return;
    }

    if (_cancelToken.isCancelled) {
      _cancelToken = CancelToken();
    }

    setState(() {
      _isSubmitting = true;
      _bannerError = null;
    });

    final username = _form.control('username').value as String;
    final password = _form.control('password').value as String;
    final loginInfo = LoginInfo(username: username, password: password);

    await LoginAction(
      cancelToken: _cancelToken,
      onSuccess: (jwtToken) async {
        setState(() {
          _isSubmitting = false;
        });

        final db = await getDatabase();
        final tokenDao = JwtTokenDao(database: db);
        final jwtTokenEntity = JwtTokenEntityBuilder.create(jwtToken).build();
        await tokenDao.saveToken(jwtTokenEntity);

        if (context.mounted) {
          try {
            final newAuthData =
                await processAndRetrieveToken(cancelToken: _cancelToken);
            if (context.mounted) {
              final UserAuthData authData =
                  Provider.of<UserAuthData>(context, listen: false);
              authData.updateUserData(newAuthData);
              context.beamToNamed('/home');
            }
          } catch (e) {
            setState(() {
              _bannerError = "Error processing authentication: ${e.toString()}";
            });
          }
        }
      },
      onError: (exception, loginInfo) {
        setState(() {
          _isSubmitting = false;
          if (exception is AppException) {
            _bannerError = _onLoginFailure(exception, loginInfo);
          } else {
            _bannerError = "An unexpected error occurred. Please try again.";
          }
        });
      },
    ).submitData(loginInfo);
  }

  @override
  void dispose() {
    if (!_cancelToken.isCancelled) {
      _cancelToken.cancel();
    }
    super.dispose();
  }

  Widget _buildProgressWidget() {
    return ProgressBarWidget(
      label: 'Waiting for user login...',
      onCancel: () {
        _cancelToken.cancel('Cancelling user login');
        setState(() {
          _isSubmitting = false;
        });
      },
    );
  }

  ReactiveForm _buildLoginForm(
      BuildContext context, ButtonStyle buttonStyle, TextStyle buttonTxtStyle) {
    return ReactiveForm(
      formGroup: _form,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              const SizedBox(height: 40),
              Icon(
                Icons.account_circle,
                size: 100,
                color: Colors.blue.shade300,
              ),
              const SizedBox(height: 20),
              Text(
                'Login to Konbit',
                style: Theme.of(context).textTheme.headlineMedium,
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 40),
              if (_bannerError != null)
                ErrorBannerWidget(
                  text: _bannerError!,
                  onDismiss: () {
                    setState(() {
                      _bannerError = null;
                    });
                  },
                ),
              const SizedBox(height: 10),
              TextFieldWidget(
                formControlName: 'username',
                labelText: 'Username',
                onSubmitted: (formControl) async {
                  await _handleUserLogin();
                },
                prefixIcon: const Icon(Icons.person),
                validationMessages: {
                  'required': (text) => 'Username must not be empty',
                  'minLength': (text) =>
                      'Username must have at least 3 characters',
                },
              ),
              const SizedBox(height: 16),
              TextFieldWidget(
                formControlName: 'password',
                labelText: 'Password',
                prefixIcon: const Icon(Icons.lock),
                obscureText: true,
                onSubmitted: (formControl) async {
                  await _handleUserLogin();
                },
                validationMessages: {
                  'required': (text) => 'Password must not be empty',
                  'minLength': (text) =>
                      'Password must have at least 7 characters',
                },
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                style: buttonStyle,
                onPressed: _handleUserLogin,
                child: Text('LOGIN', style: buttonTxtStyle),
              ),
              const SizedBox(height: 16),
              TextButton(
                onPressed: () => context.beamToNamed('/register'),
                child: const Text("Don't have an account? Sign up"),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
