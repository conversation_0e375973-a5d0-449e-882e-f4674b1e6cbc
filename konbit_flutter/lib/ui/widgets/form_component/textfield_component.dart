import 'package:flutter/material.dart';
import 'package:reactive_forms/reactive_forms.dart';

class TextFieldWidget extends StatefulWidget {
  const TextFieldWidget(
      {super.key,
      required String formControlName,
      Widget? prefixIcon,
      required String labelText,
      String? hintText,
      Widget? suffixIcon,
      bool obscureText = false,
      int? maxLines = 1,
      Map<String, String Function(Object)>? validationMessages,
      void Function(FormControl<dynamic>)? onChanged,
      void Function(FormControl<dynamic>)? onSubmitted})
      : _prefixIcon = prefixIcon,
        _labelText = labelText,
        _hintText = hintText,
        _suffixIcon = suffixIcon,
        _formControlName = formControlName,
        _maxLines = maxLines,
        _obscureText = obscureText,
        _validationMessages = validationMessages,
        _onChanged = onChanged,
        _onSubmitted = onSubmitted;

  final Widget? _prefixIcon;
  final String _labelText;
  final String? _hintText;
  final Widget? _suffixIcon;
  final String _formControlName;
  final bool _obscureText;
  final Map<String, String Function(Object)>? _validationMessages;
  final int? _maxLines;
  final void Function(FormControl<dynamic>)? _onChanged;
  final void Function(FormControl<dynamic>)? _onSubmitted;

  @override
  State<TextFieldWidget> createState() => _TextFieldWidgetState();
}

class _TextFieldWidgetState extends State<TextFieldWidget> {
  late bool _isTextHidden = widget._obscureText;

  @override
  Widget build(BuildContext context) {
    return ReactiveTextField(
      formControlName: widget._formControlName,
      decoration: !widget._obscureText
          ? InputDecoration(
              prefixIcon: widget._prefixIcon,
              labelText: widget._labelText,
              hintText: widget._hintText,
              suffixIcon: widget._suffixIcon,
            )
          : InputDecoration(
              prefixIcon: widget._prefixIcon,
              labelText: widget._labelText,
              suffixIcon: IconButton(
                  onPressed: () {
                    setState(() {
                      _isTextHidden = !_isTextHidden;
                    });
                  },
                  icon: _isTextHidden
                      ? const Icon(Icons.visibility)
                      : const Icon(Icons.visibility_off))),
      maxLines: widget._maxLines,
      obscureText: _isTextHidden,
      validationMessages: widget._validationMessages,
      onSubmitted: widget._onSubmitted,
      onChanged: widget._onChanged,
    );
  }
}
