import 'dart:collection';

import 'package:flutter/material.dart';
import 'package:reactive_forms/reactive_forms.dart';

class TextTagWidget extends StatefulWidget {
  const TextTagWidget(
      {super.key,
      required String formControlName,
      required String labelText,
      String? hintText,
      ValueChanged<String>? onTagAdded,
      ValueChanged<String>? onTagDeleted})
      : _formControlName = formControlName,
        _labelText = labelText,
        _hintText = hintText,
        _onTagAdded = onTagAdded,
        _onTagDeleted = onTagDeleted;

  final String _formControlName;
  final String _labelText;
  final String? _hintText;
  final ValueChanged<String>? _onTagAdded;
  final ValueChanged<String>? _onTagDeleted;

  @override
  State<TextTagWidget> createState() => _TextTagWidgetState();
}

class _TextTagWidgetState extends State<TextTagWidget> {
  late TextEditingController _textEditingController;
  late Set<String> tags;
  String? _currentTag;

  @override
  void initState() {
    super.initState();
    _textEditingController = TextEditingController();
    tags = SplayTreeSet<String>(
      (key1, key2) => key1.toLowerCase().compareTo(key2.toLowerCase()),
    );
  }

  @override
  void dispose() {
    super.dispose();
    _textEditingController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        ReactiveTextField(
          formControlName: widget._formControlName,
          controller: _textEditingController,
          decoration: InputDecoration(
              labelText: widget._labelText,
              hintText: widget._hintText ?? 'Input Tag and press enter'),
          onSubmitted: (formControl) {
            formControl.focus();
            //formControl.markAsTouched(updateParent: false);

            final tag = formControl.value?.toString().trim();
            if (tag != null && tag.isNotEmpty) {
              _textEditingController.clear();
              formControl.value = null;
              debugPrint('Value submitted : $tag');
              //if Tag is added successfully will return true
              if (tags.add(tag)) {
                if (widget._onTagAdded != null) {
                  widget._onTagAdded!(tag);
                }
              }
            }
            setState(() {
              _currentTag = tag;
            });
          },
        ),
        const SizedBox(
          height: 8.0,
        ),
        Wrap(spacing: 10.0, alignment: WrapAlignment.start, children: [
          for (final tag in tags)
            TagWidget(
              tag: tag,
              backgroundColor:
                  _currentTag == tag ? Colors.amber.shade100 : null,
              onTagDeleted: () {
                if (tags.remove(tag)) {
                  _currentTag = null;
                  setState(() {
                    if (widget._onTagDeleted != null) {
                      widget._onTagDeleted!(tag);
                    }
                  });
                }
              },
            )
        ]),
      ],
    );
  }
}

class TagWidget extends StatelessWidget {
  const TagWidget({
    super.key,
    required String tag,
    Color? backgroundColor,
    Function()? onTagDeleted,
  })  : _tag = tag,
        _backgroundColor = backgroundColor,
        _onTagDeleted = onTagDeleted;

  final String _tag;
  final Function()? _onTagDeleted;
  final Color? _backgroundColor;

  @override
  Widget build(BuildContext context) {
    return RawChip(
      label: Text(_tag),
      visualDensity: VisualDensity.adaptivePlatformDensity,
      onDeleted: _onTagDeleted,
      backgroundColor: _backgroundColor,
      onPressed: () {},
    );
  }
}
