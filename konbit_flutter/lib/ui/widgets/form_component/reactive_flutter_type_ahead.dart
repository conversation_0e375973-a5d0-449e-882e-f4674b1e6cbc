import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_typeahead/flutter_typeahead.dart';
import 'package:reactive_forms/reactive_forms.dart';

class ReactiveTypeAhead<T> extends ReactiveFormField<T, String> {
  ReactiveTypeAhead({
    super.key,
    super.formControl,
    super.formControlName,
    bool hideOnEmpty = false,
    super.valueAccessor,
    Widget Function(BuildContext context, T data)? itemBuilder,
    void Function(T data)? onSelected,
    void Function(FormControl<T> control)? onChanged,
    void Function(FormControl<T>)? onSubmitted,
    required FutureOr<List<T>?> Function(String search) suggestionsCallback,
    InputDecoration decoration = const InputDecoration(),
  }) : super(builder: (ReactiveFormFieldState<T, String> fieldState) {
          return TypeAheadField<T>(
              hideOnEmpty: hideOnEmpty,
              itemBuilder: (BuildContext context, T data) {
                return itemBuilder != null
                    ? itemBuilder(context, data)
                    : ListTile(
                        visualDensity: VisualDensity.standard,
                        title: Text(data.toString()),
                      );
              },
              onSelected: (T value) {
                fieldState.control.value = value;
                if (onSelected != null) {
                  onSelected(value);
                }
              },
              suggestionsCallback: suggestionsCallback,
              builder: (context, TextEditingController controller, focusNode) {
                return ReactiveTextField<T>(
                  valueAccessor: valueAccessor,
                  formControl: formControl,
                  formControlName: formControlName,
                  controller: controller,
                  focusNode: focusNode,
                  decoration: decoration,
                  onChanged: onChanged,
                  onSubmitted: onSubmitted,
                );
              });
        });
}
