import 'package:flutter/material.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class GMapWidget extends StatefulWidget {
  const GMapWidget(
      {super.key,
      required LatLng cameraCoords,
      required void Function(GoogleMapController controller) onMapCreated,
      double zoom = 0.0,
      Set<Marker> markers = const <Marker>{},
      Set<ClusterManager> clusterManagers = const <ClusterManager>{}})
      : _cameraCoords = cameraCoords,
        _zoom = zoom,
        _markers = markers,
        _clusterManagers = clusterManagers,
        _onMapCreated = onMapCreated;

  final LatLng _cameraCoords;
  final double _zoom;
  final Set<ClusterManager> _clusterManagers;
  final Set<Marker> _markers;
  final void Function(GoogleMapController controller) _onMapCreated;

  @override
  State<GMapWidget> createState() => _GMapWidgetState();
}

class _GMapWidgetState extends State<GMapWidget> {
  @override
  Widget build(BuildContext context) {
    return GoogleMap(
      onMapCreated: widget._onMapCreated,
      initialCameraPosition:
          CameraPosition(target: widget._cameraCoords, zoom: widget._zoom),
      clusterManagers: widget._clusterManagers,
      markers: widget._markers,
      zoomControlsEnabled: true,
      myLocationButtonEnabled: true,
      myLocationEnabled: true,
    );
  }
}
