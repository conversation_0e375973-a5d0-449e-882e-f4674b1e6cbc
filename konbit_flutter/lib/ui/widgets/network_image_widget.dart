import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/config/konbit_cache_manager.dart';

import '../../core/api/interceptor/http_interceptor.dart';
import '../../core/config/konbit_config.dart';
import '../../core/logger/logger.dart';

class NetworkImageWidget extends StatelessWidget {
  const NetworkImageWidget(
      {super.key, required this.imageUriPath, this.fallBackWidget});

  final String imageUriPath;
  final Widget? fallBackWidget;

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      cacheManager: KonbitCacheManager.instance,
      imageUrl: Uri(
              scheme: KonbitConfig.getConfig().httpScheme,
              host: KonbitConfig.getConfig().server,
              port: KonbitConfig.getConfig().port,
              path: imageUriPath)
          .toString(),
      httpHeaders: {
        "Authorization":
            'Bearer ${AccessTokenInterceptor.getSingleInstance().accessToken}',
      },
      imageBuilder: (context, imageProvider) => Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          image: DecorationImage(image: imageProvider, fit: BoxFit.cover),
        ),
      ),
      placeholder: (context, url) => const SizedBox(
        width: 40.0,
        height: 40.0,
        child: CircularProgressIndicator(),
      ),
      errorWidget: (context, url, error) {
        logger.warning('Failed to load image with error: $error');
        return fallBackWidget ??
            const SizedBox(
              width: 40.0,
              height: 40.0,
              child: Icon(
                Icons.account_circle,
                size: 40.0,
              ),
            );
      },
    );
  }
}
