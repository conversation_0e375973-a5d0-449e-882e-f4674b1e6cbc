import 'package:flutter/material.dart';

class ErrorBannerWidget extends StatelessWidget {
  const ErrorBannerWidget(
      {super.key, required String text, required Function() onDismiss})
      : _text = text,
        _onDismiss = onDismiss;
  final String _text;
  final Function() _onDismiss;

  @override
  Widget build(BuildContext context) {
    return MaterialBanner(
      padding: const EdgeInsets.all(20),
      content: Text(_text, style: const TextStyle(color: Colors.white)),
      leading: const Icon(Icons.error_rounded),
      backgroundColor: Colors.deepOrange,
      actions: <Widget>[
        TextButton(
          onPressed: _onDismiss,
          child: const Text('DISMISS'),
        ),
      ],
    );
  }
}
