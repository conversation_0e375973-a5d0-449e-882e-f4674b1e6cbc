import 'package:beamer/beamer.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:hugeicons/hugeicons.dart';
import 'package:konbit_flutter/core/model/user_auth_data.dart';
import 'package:konbit_flutter/ui/widgets/dialog/konbit_config_dialog.dart';
import 'package:provider/provider.dart';

import '../../core/api/profile_picture_api.dart';
import '../../core/database/db_helper.dart';
import '../../core/database/jwt_token_dao.dart';
import '../../core/helper/api_helper.dart';
import '../../core/helper/token_helper.dart';
import '../../core/logger/logger.dart';
import '../../core/model/db_entity/jwt_token_entity.dart';
import '../../core/model/output/http_konbit_message.dart';
import 'network_image_widget.dart';

class AppDrawer extends StatelessWidget {
  const AppDrawer({super.key});

  Future<void> _logout(BuildContext context) async {
    JwtTokenDao? tokenDao;
    bool tokenDeleted = false;
    try {
      final database = await getDatabase();
      tokenDao = JwtTokenDao(database: database);
    } catch (error) {
      logger.error('Failed to retrieve database handle : $error');
    }

    JwtTokenEntity? jwtTokenEntity;
    try {
      jwtTokenEntity = await tokenDao?.getToken();
      logger.info('Deleting user Token');
      await tokenDao?.deleteToken();
      tokenDeleted = true;
    } catch (error) {
      logger.error('Failed to process token from database : $error');
    }

    Response<HttpKonbitMessage>? response;
    if (jwtTokenEntity != null) {
      try {
        final userApi =
            UserApiBuilder.withAuthentication(CancelToken()).build();
        logger.info('Logging out user session on Konbit Server');
        response = await userApi.logout(jwtTokenEntity.refreshToken);
      } catch (error) {
        logger
            .error('Failed to clear user session from Konbit Server : $error');
      }
    }

    if (response != null && response.statusCode == 200) {
      logger.info('User Session has been logged out successfully');
    }

    if (response != null && response.statusCode != 200) {
      HttpKonbitMessage? httpMessage = response.data;
      logger.info('Failure to logout user session : ${httpMessage?.toJson()}');
    }
    if (tokenDeleted && context.mounted) {
      final userAuthData = Provider.of<UserAuthData>(context, listen: false);
      userAuthData.updateUserData(UserAuthData());
      context.beamToNamed("/");
    }
  }

  @override
  Widget build(BuildContext context) {
    final authData = Provider.of<UserAuthData>(context, listen: false);

    String username =
        authData.token != null ? getUsernameFromToken(authData.token!) : "";

    final headerIcon = Icon(
      HugeIcons.strokeRoundedUserSquare,
      weight: 50.0,
      size: 45,
      color: Colors.grey.shade50,
    );

    final headerImage = CircleAvatar(
      radius: 50.0,
      child: NetworkImageWidget(
        imageUriPath: '${ProfilePictureApi.baseUri}/profile/$username',
        fallBackWidget: headerIcon,
      ),
    );

    final headerTitle = Text(
      username,
      style: TextStyle(
          fontWeight: FontWeight.w600,
          fontSize: 20.0,
          color: Colors.grey.shade100),
    );

    return Drawer(
      // Add a ListView to the drawer. This ensures the user can scroll
      // through the options in the drawer if there isn't enough vertical
      // space to fit everything.
      child: ListView(
        // Important: Remove any padding from the ListView.
        padding: EdgeInsets.zero,
        children: [
          DrawerHeader(
            decoration: BoxDecoration(
              color: Colors.blue,
            ),
            child: username.length < 18
                ? Row(children: [
                    headerImage,
                    SizedBox(
                      width: 5.0,
                    ),
                    headerTitle
                  ])
                : Column(children: [
                    headerImage,
                    SizedBox(
                      height: 2.0,
                    ),
                    headerTitle
                  ]),
          ),
          ListTile(
            title: const Text('Settings'),
            //selected: _selectedIndex == 0,
            onTap: () async {
              // Update the state of the app
              await KonbitConfigDialog.show(context);
              // Then close the drawer
              Navigator.pop(context);
            },
          ),
          ListTile(
              title: const Text('Logout'),
              //selected: _selectedIndex == 1,
              onTap: () async {
                await _logout(context);
              }),
        ],
      ),
    );
  }
}
