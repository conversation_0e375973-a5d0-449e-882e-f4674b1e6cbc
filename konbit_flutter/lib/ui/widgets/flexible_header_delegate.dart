import 'package:flutter/material.dart';

class FlexibleHeaderDelegate extends SliverPersistentHeaderDelegate {
  FlexibleHeaderDelegate({required String title}) : _title = title;
  final String _title;

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return Container(
      height: 50,
      color: Colors.blue,
      child: Text(
        _title,
        style: TextStyle(fontSize: 18.0),
      ),
    );
  }

  @override
  double get maxExtent => throw 70.0;

  @override
  double get minExtent => 60.0;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) {
    return true;
  }
}
