import 'package:flutter/material.dart';
import 'package:sheet/sheet.dart';

class SlidingWidget extends StatefulWidget {
  const SlidingWidget(
      {super.key,
      required Widget title,
      double initialExtent = 50,
      double minExtent = 50,
      required SheetController sheetController,
      required Widget child})
      : _title = title,
        _initialExtent = initialExtent,
        _minExtent = minExtent,
        _sheetController = sheetController,
        _child = child;
  final Widget _title;
  final double _initialExtent;
  final double _minExtent;
  final SheetController _sheetController;
  final Widget _child;

  @override
  State<SlidingWidget> createState() => _SlidingWidgetState();
}

class _SlidingWidgetState extends State<SlidingWidget> {
  @override
  Widget build(BuildContext context) {
    const borderRadius = BorderRadius.only(
        topLeft: Radius.circular(20.0), topRight: Radius.circular(20.0));

    return DefaultSheetController(
      child: Sheet(
          controller: widget._sheetController,
          initialExtent: widget._initialExtent,
          minExtent: widget._minExtent,
          physics: const SnapSheetPhysics(
            stops: <double>[0, 0.1, 0.3, 1],
          ),
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
              borderRadius: borderRadius,
              side: BorderSide(color: Colors.black)),
          child: Padding(
            padding: const EdgeInsets.all(8.0),
            child: Scaffold(
              appBar: AppBar(
                title: widget._title,
                leading: IconButton(
                    icon: Icon(
                      Icons.arrow_circle_up_outlined,
                      size: 30,
                    ),
                    onPressed: () => widget._sheetController.animateTo(1024.0,
                        duration: Duration(milliseconds: 500),
                        curve: Curves.easeInOutCubic)),
                actions: [
                  IconButton(
                      icon: Icon(
                        Icons.arrow_circle_down_outlined,
                        size: 30,
                      ),
                      onPressed: () => widget._sheetController.animateTo(30.0,
                          duration: Duration(milliseconds: 500),
                          curve: Curves.easeInOutCubic))
                ],
                automaticallyImplyLeading: false,
                centerTitle: true,
                //shadowColor: Colors.transparent,
                backgroundColor: Colors.white,
                bottomOpacity: 1.0,
                scrolledUnderElevation: 0.0,
              ),
              body: widget._child,
            ),
          )),
    );
  }
}
