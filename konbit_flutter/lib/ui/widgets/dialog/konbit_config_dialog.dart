import 'package:flutter/material.dart';
import 'package:konbit_flutter/core/config/konbit_config.dart';
import 'package:konbit_flutter/core/helper/shared_preferences_helper.dart';
import 'package:konbit_flutter/ui/widgets/form_component/textfield_component.dart';
import 'package:quickalert/models/quickalert_type.dart';
import 'package:quickalert/widgets/quickalert_dialog.dart';
import 'package:reactive_forms/reactive_forms.dart';

class _KonbitConfigForm extends StatefulWidget {
  _KonbitConfigForm();

  @override
  State<_KonbitConfigForm> createState() => _KonbitConfigFormState();

  final _form = FormGroup({
    'protocol': FormControl<String>(
        value: KonbitConfig.getConfig().httpScheme,
        validators: [Validators.required]),
    'server': FormControl<String>(
        value: KonbitConfig.getConfig().server,
        validators: [Validators.required]),
    'port': FormControl<int>(value: KonbitConfig.getConfig().port, validators: [
      Validators.required,
      Validators.number(allowNegatives: false, allowNull: false)
    ]),
  });

  FormGroup get form => _form;
}

class _KonbitConfigFormState extends State<_KonbitConfigForm> {
  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: ReactiveForm(
          formGroup: widget._form,
          child: Column(
            children: <Widget>[
              ReactiveDropdownField<String>(
                formControlName: 'protocol',
                decoration: InputDecoration(labelText: 'Protocol'),
                hint: Text('Select http protocol...'),
                items: [
                  DropdownMenuItem(
                    value: 'http',
                    child: Text('http'),
                  ),
                  DropdownMenuItem(
                    value: 'https',
                    child: Text('https'),
                  ),
                ],
              ),
              TextFieldWidget(
                  formControlName: 'server', labelText: 'Konbit Server'),
              TextFieldWidget(
                  formControlName: 'port', labelText: 'Konbit Port'),
            ],
          )),
    );
  }
}

class KonbitConfigDialog {
  static Future<dynamic> show(BuildContext context) {
    final _KonbitConfigForm configForm = _KonbitConfigForm();
    return QuickAlert.show(
      context: context,
      type: QuickAlertType.confirm,
      title: 'Are you sure you want to change these settings?',
      text: 'Konbit Server Settings',
      barrierDismissible: true,
      confirmBtnText: 'Save',
      widget: configForm,
      onConfirmBtnTap: () async {
        if (!configForm.form.valid) {
          configForm.form.markAllAsTouched();
          return;
        }

        final formControls = configForm.form.controls;
        KonbitConfig.getConfig()
          ..httpScheme = formControls['protocol']!.value! as String
          ..server = formControls['server']!.value! as String
          ..port = formControls['port']!.value! as int;

        await updateKonbitConfig();

        Navigator.pop(context);
      },
    );
  }
}
