import 'package:flutter/material.dart';
import 'package:konbit_flutter/ui/widgets/dialog/konbit_config_dialog.dart';

class AppBarWidget extends StatelessWidget {
  const AppBarWidget({super.key, String? title}) : _title = title;
  final String? _title;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(_title ?? 'Konbit',
          style: Theme.of(context).textTheme.titleLarge),
      trailing: Icon<PERSON><PERSON>on(
        icon: Icon(Icons.settings_rounded),
        onPressed: () async {
          await KonbitConfigDialog.show(context);
        },
      ),
    );
  }
}
