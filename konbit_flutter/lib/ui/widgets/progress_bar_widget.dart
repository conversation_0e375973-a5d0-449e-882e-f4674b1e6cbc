import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';

class ProgressBarWidget extends StatelessWidget {
  const ProgressBarWidget(
      {super.key,
      String label = 'Loading...',
      String caption = 'Please wait',
      void Function()? onCancel})
      : _label = label,
        _caption = caption,
        _onCancel = onCancel;

  final String _label;
  final String _caption;
  final void Function()? _onCancel;

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          _label,
          style: Theme.of(context).textTheme.titleLarge,
        ),
        const SizedBox(
          height: 8.0,
        ),
        /*Center(
            child: CircularProgressIndicator(
          semanticsLabel: _purpose,
          semanticsValue: 'Loading..',
        )),*/
        Text(_caption),
        Sized<PERSON>ox(
          height: 8.0,
        ),
        const SpinKitWaveSpinner(
          color: Colors.black38,
          size: 70.0,
        ),
        SizedBox(
          height: 8.0,
        ),
        if (_onCancel != null)
          TextButton(
              onPressed: _onCancel,
              child: const Text(
                'cancel',
                style: TextStyle(
                    fontSize: 18.0, decoration: TextDecoration.underline),
              )),
      ],
    );
  }
}
