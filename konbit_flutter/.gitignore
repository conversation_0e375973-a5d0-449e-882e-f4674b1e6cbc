# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# The .vscode folder contains launch configuration and tasks you configure in
# VS Code which you may wish to be included in version control, so this line
# is commented out by default.
#.vscode/

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

#Added by <PERSON> to ignore files generated by json generator and open api generator package.
**/*.g.dart
**/*.mocks.dart

#**/lib/model/**/**.g.dart

#Need to add new entry
api/**
# Needed below lines not to be ignored by git.
#Adding new ones here if there are newly openapi json packages added.
!api/user_api
!api/user_profile_api

#Not ignoring some pubspec.yaml and docs readme files
!/api/**/pubspec.yaml
#!/api/**/doc
#!/api/**/doc/*.md

/openapi_generator_config.json
/android/app/.cxx/

#FVM specific project directories
.fvm/
